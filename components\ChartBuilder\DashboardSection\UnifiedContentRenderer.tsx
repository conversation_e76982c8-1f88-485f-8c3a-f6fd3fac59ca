'use client'

import { ReactNode, useEffect, useState } from 'react';
import { DashboardItem, SavedChart, TableItem, PythonPlotItem, CalculatorResultItem, TextItem } from './types';
import { EnhancedChartVisualizer } from '../ChartSectionsConf/EnhancedChartVisualizer';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Calculator, BarChart3, TableIcon, ImageIcon, Loader2, FileText } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';

interface UnifiedContentRendererProps {
  item: DashboardItem;
  isEditMode: boolean;
  isDashboardChart?: boolean;
}

interface ContentData {
  content: ReactNode;
  icon: ReactNode;
  title: string;
  subtitle: string;
  description?: string;
}

export function useUnifiedContentRenderer({
  item,
  isEditMode,
  isDashboardChart = true
}: UnifiedContentRendererProps): ContentData {
  
  // Chart Content Renderer with enhanced fullscreen support
  const renderChartContent = (chart: SavedChart) => {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <EnhancedChartVisualizer
          data={chart.data}
          initialChartType={chart.chartType || chart.config.type}
          chartConfig={{
            type: chart.config.type,
            xAxis: chart.config.xAxis,
            yAxis: chart.config.yAxis,
            title: chart.config.title,
            description: chart.config.description,
            showLegend: chart.config.showLegend,
            showLabels: chart.config.showLabels,
            showGrid: chart.config.showGrid,
            color: chart.config.color,
            customLabel: chart.config.customLabel,
            aggregation: chart.config.aggregation,
            groupBy: chart.config.groupBy,
            timeScale: chart.config.timeScale,
            enableZoom: chart.config.enableZoom,
            multiSeries: chart.config.multiSeries,
            fontSize: chart.config.fontSize
          }}
          showConfig={false}
          fullHeight={true}
          isDashboardChart={isDashboardChart}
          className="w-full h-full"
        />
      </div>
    );
  };

  // Table Content Renderer
  const renderTableContent = (table: TableItem) => {
    return (
      <div className="w-full h-full overflow-auto">
        <Table>
          <TableHeader>
            <TableRow>
              {table.columns && table.columns.map((column, index) => (
                <TableHead key={index} className="text-xs py-1 px-2">
                  {column}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {table.data && table.data.map((row, rowIndex) => (
              <TableRow key={rowIndex}>
                {table.columns && table.columns.map((column, colIndex) => (
                  <TableCell key={colIndex} className="text-xs py-1 px-2">
                    {row[column] !== undefined ? String(row[column]) : ''}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  };

  // Python Plot Content Renderer with 3D Plotly support
  const renderPythonPlotContent = (plot: PythonPlotItem) => {
    const [plotlyLoaded, setPlotlyLoaded] = useState(false);
    const [isInteractivePlot, setIsInteractivePlot] = useState(false);
    const [plotlyData, setPlotlyData] = useState<any>(null);

    // Load Plotly.js dynamically if needed
    useEffect(() => {
      if (typeof window !== 'undefined' && !window.Plotly) {
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/plotly.js-dist@latest/plotly.js';
        script.crossOrigin = 'anonymous';
        script.onload = () => {
          setPlotlyLoaded(true);
        };
        script.onerror = () => {
          console.error('Failed to load Plotly.js');
        };
        document.head.appendChild(script);
      } else if (window.Plotly) {
        setPlotlyLoaded(true);
      }
    }, []);

    // Check if plot is interactive Plotly data
    useEffect(() => {
      if (plot.plotUrl && typeof plot.plotUrl === 'string') {
        try {
          // Check if plotUrl contains JSON data (for interactive plots)
          if (plot.plotUrl.startsWith('{') || plot.plotUrl.includes('plotly')) {
            const plotData = JSON.parse(plot.plotUrl);
            if (plotData.data && plotData.layout) {
              setIsInteractivePlot(true);
              setPlotlyData(plotData);
            }
          }
        } catch (error) {
          // Not JSON data, treat as regular image URL
          setIsInteractivePlot(false);
        }
      }
    }, [plot.plotUrl]);

    return (
      <div className="w-full h-full flex items-center justify-center">
        {plot.plotUrl ? (
          <div className="relative w-full h-full">
            {isInteractivePlot && plotlyData ? (
              // Render interactive Plotly plot (including 3D)
              <div className="w-full h-full">
                {plotlyLoaded ? (
                  <div
                    className="w-full h-full border rounded"
                    ref={(el) => {
                      if (el && plotlyLoaded && (window as any).Plotly && plotlyData) {
                        (window as any).Plotly.newPlot(el, plotlyData.data || [], plotlyData.layout || {}, {
                          responsive: true,
                          displayModeBar: true,
                          displaylogo: false,
                          modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
                          toImageButtonOptions: {
                            format: 'png',
                            filename: plot.title || 'plot',
                            height: 500,
                            width: 700,
                            scale: 1
                          }
                        }).catch(console.error);
                      }
                    }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    <span className="ml-2 text-sm text-muted-foreground">Loading interactive plot...</span>
                  </div>
                )}
              </div>
            ) : (
              // Render static image
              <img
                src={plot.plotUrl}
                alt={plot.title || "Python Plot"}
                className="object-contain w-full h-full"
                style={{ maxWidth: '100%', maxHeight: '100%' }}
              />
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center text-muted-foreground">
            <ImageIcon className="h-8 w-8 mb-2" />
            <p className="text-sm">No plot available</p>
          </div>
        )}
      </div>
    );
  };

  // Calculator Result Content Renderer
  const renderCalculatorContent = (calc: CalculatorResultItem) => {
    return (
      <div className="w-full h-full flex flex-col items-center justify-center p-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-primary mb-2">
            {calc.formattedResult || calc.result}
          </div>
          <div className="text-sm text-muted-foreground mb-2">
            {calc.formula}
          </div>
          <div className="text-xs text-muted-foreground">
            {new Date(calc.timestamp).toLocaleString()}
          </div>
        </div>
      </div>
    );
  };

  // Text Content Renderer
  const renderTextContent = (textItem: TextItem) => {
    return (
      <div className="w-full h-full overflow-auto p-4">
        <div className="prose prose-sm max-w-none dark:prose-invert">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw]}
            className="break-words"
          >
            {textItem.content || '*No content available*'}
          </ReactMarkdown>
        </div>
      </div>
    );
  };

  // Get content icon based on type
  const getContentIcon = () => {
    switch (item.type) {
      case 'chart':
        return <BarChart3 className="h-3 w-3" />;
      case 'table':
        return <TableIcon className="h-3 w-3" />;
      case 'pythonplot':
        return <ImageIcon className="h-3 w-3" />;
      case 'calculator':
        return <Calculator className="h-3 w-3" />;
      case 'text':
        return <FileText className="h-3 w-3" />;
      default:
        return null;
    }
  };

  // Get content title
  const getContentTitle = () => {
    switch (item.type) {
      case 'chart':
        const chart = item as SavedChart;
        return chart.title || chart.config.title || 'Untitled Chart';
      case 'table':
        const table = item as TableItem;
        return table.title || 'Data Table';
      case 'pythonplot':
        const plot = item as PythonPlotItem;
        return plot.title || 'Python Plot';
      case 'calculator':
        const calc = item as CalculatorResultItem;
        return calc.title || 'Calculator Result';
      case 'text':
        const textItem = item as TextItem;
        return textItem.content ? (textItem.content.length > 30 ? textItem.content.substring(0, 30) + '...' : textItem.content) : 'Text Content';
      default:
        return 'Dashboard Item';
    }
  };

  // Get content subtitle
  const getContentSubtitle = () => {
    switch (item.type) {
      case 'chart':
        const chart = item as SavedChart;
        return chart.chartType || chart.config.type || 'bar';
      case 'table':
        return 'table';
      case 'pythonplot':
        return 'plot';
      case 'calculator':
        return 'calc';
      case 'text':
        return 'markdown';
      default:
        return item.type;
    }
  };

  // Get content description
  const getContentDescription = () => {
    switch (item.type) {
      case 'chart':
        const chart = item as SavedChart;
        return chart.description || chart.config.description;
      case 'table':
        const table = item as TableItem;
        return table.description;
      case 'pythonplot':
        const plot = item as PythonPlotItem;
        return plot.description;
      case 'calculator':
        const calc = item as CalculatorResultItem;
        return calc.description;
      case 'text':
        const textItem = item as TextItem;
        return textItem.content ? `${textItem.content.length} characters` : 'Empty text content';
      default:
        return undefined;
    }
  };

  // Render content based on type
  const renderContent = () => {
    switch (item.type) {
      case 'chart':
        return renderChartContent(item as SavedChart);
      case 'table':
        return renderTableContent(item as TableItem);
      case 'pythonplot':
        return renderPythonPlotContent(item as PythonPlotItem);
      case 'calculator':
        return renderCalculatorContent(item as CalculatorResultItem);
      case 'text':
        return renderTextContent(item as TextItem);
      default:
        return (
          <div className="w-full h-full flex items-center justify-center text-muted-foreground">
            <p>Unsupported content type: {item.type}</p>
          </div>
        );
    }
  };

  return {
    content: renderContent(),
    icon: getContentIcon(),
    title: getContentTitle(),
    subtitle: getContentSubtitle(),
    description: getContentDescription()
  };
}

export default useUnifiedContentRenderer;

// Extend window type for Plotly
declare global {
  interface Window {
    Plotly: any;
  }
}
