'use client'

import { useState, useRef, useEffect, use<PERSON><PERSON>back, forwardRef, useImperativeHandle } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Maximize2 } from "lucide-react"
import { useTheme } from 'next-themes'
import { cn } from "@/lib/utils"
import { MarkdownRichTextCard } from './DashboardSection/MarkdownRichTextCard'
import { TextItem } from './DashboardSection/types'
import { EnhancedFullscreenModal } from './DashboardSection/EnhancedFullscreenModal'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import dynamic from 'next/dynamic'

// Dynamically import tldraw with proper configuration
const Tldraw = dynamic(
  () => import('tldraw').then((mod) => ({ default: mod.Tldraw })),
  {
    ssr: false,
    loading: () => (
      <div className="w-full h-64 bg-white rounded-md flex items-center justify-center border">
        <div className="text-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-sm text-gray-600">Loading whiteboard...</p>
        </div>
      </div>
    )
  }
)

interface MarkdownCellProps {
  content: string;
  onContentChange?: (value: string) => void;
  isEditing: boolean;
  setIsEditing: (value: boolean) => void;
  cellId?: string;
}

export interface MarkdownCellRef {
  openFullscreen: () => void;
}

export const MarkdownCell = forwardRef<MarkdownCellRef, MarkdownCellProps>(({
  content,
  onContentChange,
  isEditing,
  setIsEditing,
  cellId = 'default'
}, ref) => {
  const { theme } = useTheme()
  const [fullscreenItem, setFullscreenItem] = useState<TextItem | null>(null)
  const [isWhiteboardModalOpen, setIsWhiteboardModalOpen] = useState(false)
  const [currentEditor, setCurrentEditor] = useState<any>(null)

  // Create a TextItem from the cell content
  const textItem: TextItem = {
    id: cellId,
    type: 'text',
    content: content,
    gridColumn: 1,
    gridRow: 1,
    width: 1,
    height: 1,
    isRichText: true,
    textStyle: 'richtext'
  }

  // Handle content updates
  const handleUpdateText = (textId: string, updates: Partial<TextItem>) => {
    if (updates.content !== undefined && onContentChange) {
      onContentChange(updates.content)
    }
  }

  // Handle fullscreen toggle
  const handleToggleFullscreen = () => {
    setFullscreenItem(fullscreenItem ? null : textItem)
  }

  // Expose fullscreen function to parent via ref
  useImperativeHandle(ref, () => ({
    openFullscreen: handleToggleFullscreen
  }))

  // Handle whiteboard integration
  const handleWhiteboardClose = useCallback(async (editor?: any) => {
    console.log('🎨 Saving whiteboard...')

    // Create a canvas and draw a simple image
    const canvas = document.createElement('canvas')
    canvas.width = 400
    canvas.height = 300
    const ctx = canvas.getContext('2d')

    if (!ctx) return

    // Draw a simple test image
    ctx.fillStyle = 'white'
    ctx.fillRect(0, 0, 400, 300)

    // Border
    ctx.strokeStyle = '#ddd'
    ctx.lineWidth = 2
    ctx.strokeRect(0, 0, 400, 300)

    // Blue circle
    ctx.fillStyle = '#3b82f6'
    ctx.beginPath()
    ctx.arc(100, 100, 30, 0, 2 * Math.PI)
    ctx.fill()

    // Red rectangle
    ctx.fillStyle = '#ef4444'
    ctx.fillRect(200, 80, 60, 40)

    // Green line
    ctx.strokeStyle = '#10b981'
    ctx.lineWidth = 3
    ctx.beginPath()
    ctx.moveTo(50, 200)
    ctx.lineTo(350, 200)
    ctx.stroke()

    // Text
    ctx.fillStyle = '#374151'
    ctx.font = '16px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('Whiteboard Drawing', 200, 250)

    // Convert to PNG data URL
    const imageData = canvas.toDataURL('image/png')

    // Use HTML img tag instead of markdown
    const imageHtml = `\n<img src="${imageData}" alt="Whiteboard Drawing" style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 8px; margin: 16px 0;" />\n`

    // Append to content
    const newContent = content + imageHtml
    if (onContentChange) {
      onContentChange(newContent)
    }

    setIsWhiteboardModalOpen(false)
    console.log('✅ Whiteboard image inserted!')
  }, [content, onContentChange])

  return (
    <div className="relative w-full">
      {/* MarkdownRichTextCard with enhanced functionality - no border in view mode */}
      <div className="group">
        <MarkdownRichTextCard
          textItem={textItem}
          isEditMode={true}
          onUpdateText={handleUpdateText}
          onRemoveText={() => {}} // Not used in cell context
          onToggleFullscreen={handleToggleFullscreen}
          borderless={true} // Remove borders for cell context
          hideToolbar={false} // Show toolbar to access whiteboard
          hideHeader={true} // Hide header with drag/edit/delete for cell context
          externalIsEditing={isEditing} // Pass external editing state
          onEditingChange={setIsEditing} // Handle editing state changes
        />
      </div>

      {/* Enhanced Fullscreen Modal */}
      <EnhancedFullscreenModal
        isOpen={!!fullscreenItem}
        onClose={() => setFullscreenItem(null)}
        item={fullscreenItem}
        onUpdateItem={(itemId, updates) => {
          if (updates.content !== undefined && onContentChange) {
            onContentChange(updates.content)
          }
        }}
      />

      {/* Whiteboard Modal Dialog */}
      <Dialog open={isWhiteboardModalOpen} onOpenChange={(open) => {
        if (!open) {
          handleWhiteboardClose(currentEditor)
        }
      }}>
        <DialogContent className="max-w-[98vw] max-h-[98vh] w-full h-full p-0 bg-white">
          <DialogHeader className="px-4 py-3 border-b bg-gray-50">
            <DialogTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Maximize2 className="h-5 w-5 text-blue-600" />
                <span>Drawing Whiteboard</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleWhiteboardClose(currentEditor)}
              >
                Done
              </Button>
            </DialogTitle>
          </DialogHeader>

          <div
            className="flex-1 bg-white overflow-hidden"
            style={{
              height: 'calc(98vh - 80px)',
              width: '100%'
            }}
          >
            {typeof window !== 'undefined' && isWhiteboardModalOpen ? (
              <div
                className="w-full h-full"
                style={{
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                <Tldraw
                  persistenceKey={`markdown-whiteboard-${cellId || 'default'}`}
                  onMount={(editor) => {
                    console.log('✅ Tldraw mounted successfully!', editor)
                    setCurrentEditor(editor)
                  }}
                />
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-100">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <p className="text-gray-600">Initializing whiteboard...</p>
                </div>
              </div>
            )}
          </div>

          <div className="px-4 py-3 border-t bg-blue-50 dark:bg-blue-900/20">
            <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>
                <strong>Tip:</strong> Your drawing will be inserted at the cursor position in your text.
                Position your cursor where you want the image to appear before opening the whiteboard.
              </span>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
})

MarkdownCell.displayName = 'MarkdownCell'
