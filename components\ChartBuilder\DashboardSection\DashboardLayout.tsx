'use client'

/**
 * DashboardLayout Component
 *
 * This component is responsible for rendering the dashboard grid layout.
 * It handles the positioning and rendering of different types of cards (charts, headings, text)
 * in a responsive grid layout that can be rearranged in edit mode.
 *
 * Features:
 * - Responsive grid layout with drag-and-drop functionality in edit mode
 * - Supports different types of content: charts, headings, and text cards
 * - Handles layout changes and updates item positions
 */

import { useState, useEffect, useCallback, useMemo, memo, useRef } from 'react'
import GridLayout, { Layout } from 'react-grid-layout'
import 'react-grid-layout/css/styles.css'
import 'react-resizable/css/styles.css'
import { ChartVisualizer } from '../ChartVisualizer'
import { DashboardItem, SavedChart, HeadingItem, TextItem, CalculatorResultItem } from './types'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { X, GripVertical } from 'lucide-react'
import { HeadingCard } from './HeadingCard'

import { UnifiedDashboardCard } from './UnifiedDashboardCard'
import { EnhancedFullscreenModal } from './EnhancedFullscreenModal'
import { ChartUpdateModal } from './ChartUpdateModal'
import { MarkdownRichTextCard } from './MarkdownRichTextCard';
import { CalculatorResultCard } from './CalculatorResultCard'
import { toast } from 'sonner'
import '../DashboardSection/styles/dashboard-enhanced.css'

interface DashboardLayoutProps {
  items: DashboardItem[]
  onUpdateItem: (itemId: string, updatedItem: Partial<DashboardItem>) => void
  onRemoveItem: (itemId: string) => void
  onSaveLayout: (updatedItems: DashboardItem[]) => void
  isEditMode?: boolean
  viewMode?: 'grid' | 'list'
  onRefreshItem?: (itemId: string) => void
  onExportItem?: (itemId: string, format: 'png' | 'pdf' | 'csv' | 'json') => void
  onShareItem?: (itemId: string) => void
}

// Define window properties to track drag and resize operations
declare global {
  interface Window {
    isDraggingDashboardItem?: boolean;
    isResizingDashboardItem?: boolean;
    activeItemId?: string;
    lastLayoutUpdate?: number;
  }
}

// Memoized individual card component to reduce re-renders
const DashboardItemCard = memo(({
  item,
  isEditMode,
  onUpdateItem,
  onRemoveItem,
  onRefreshItem,
  onExportItem,
  onShareItem,
  isDragging,
  isResizing
}: {
  item: DashboardItem,
  isEditMode: boolean,
  onUpdateItem: (itemId: string, updates: Partial<DashboardItem>) => void,
  onRemoveItem: (itemId: string) => void,
  onRefreshItem?: (itemId: string) => void,
  onExportItem?: (itemId: string, format: 'png' | 'pdf' | 'csv' | 'json') => void,
  onShareItem?: (itemId: string) => void,
  isDragging?: boolean,
  isResizing?: boolean
}) => {
  const [fullscreenItem, setFullscreenItem] = useState<DashboardItem | null>(null);
  const [editingChart, setEditingChart] = useState<SavedChart | null>(null);

  // Handle fullscreen toggle
  const handleToggleFullscreen = (itemId: string) => {
    if (itemId === item.id) {
      const newFullscreenItem = fullscreenItem?.id === item.id ? null : item;
      setFullscreenItem(newFullscreenItem);
    }
  };

  // Handle chart editing
  const handleEditChart = (itemId: string) => {
    if (item.type === 'chart') {
      setEditingChart(item as SavedChart);
    }
  };

  // For unified card types (chart, table, pythonplot, calculator), use UnifiedDashboardCard
  if (['chart', 'table', 'pythonplot', 'calculator'].includes(item.type)) {
    return (
      <>
        <UnifiedDashboardCard
          item={item}
          isEditMode={isEditMode}
          onUpdateItem={onUpdateItem}
          onRemoveItem={onRemoveItem}
          onToggleFullscreen={handleToggleFullscreen}
          onRefreshItem={onRefreshItem}
          onEditItem={handleEditChart}
        />

        {/* Enhanced Fullscreen Modal */}
        <EnhancedFullscreenModal
          isOpen={!!fullscreenItem}
          onClose={() => setFullscreenItem(null)}
          item={fullscreenItem}
          onUpdateItem={onUpdateItem}
          onRefreshItem={onRefreshItem}
          onEditItem={handleEditChart}
          onExportItem={onExportItem}
          onShareItem={onShareItem}
        />

        {/* Chart Update Modal */}
        <ChartUpdateModal
          isOpen={!!editingChart}
          onClose={() => setEditingChart(null)}
          chart={editingChart}
          onUpdateChart={(chartId, updates) => {
            onUpdateItem(chartId, updates);
            setEditingChart(null);
          }}
        />
      </>
    );
  }

  // For table items, render a table component
  if (item.type === 'table') {
    return (
      <Card
        className={`w-full h-full overflow-auto transition-all duration-200
          ${isEditMode ? 'dashboard-card-edit' : 'dashboard-card no-border'}
          ${isDragging ? 'ring-1 ring-primary' : ''}
          ${isResizing ? 'ring-1 ring-primary' : ''}`}
        data-item-type={item.type}
        data-item-id={item.id}
      >
        {isEditMode && (
          <div className="draggable-handle absolute top-0 left-0 w-full h-6 bg-primary/5 flex items-center justify-center cursor-grab rounded-t-md">
            <GripVertical className="h-4 w-4 text-primary" />
          </div>
        )}

        {isEditMode && (
          <div className="absolute top-2 right-2 flex gap-1 z-10 non-draggable">
            <Button
              variant="ghost"
              size="icon"
              className="h-5 w-5 bg-white/90 hover:bg-red-50 hover:text-red-600 border border-red-200/50 hover:border-red-300 transition-all duration-200 rounded-full shadow-sm non-draggable"
              onClick={() => onRemoveItem(item.id)}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        )}

        <div className={`h-full dashboard-item-content p-2 ${isEditMode ? 'pt-8' : ''}`}>
          <div className="text-sm font-medium mb-2">{item.title || 'Data Table'}</div>
          <div className="max-h-[calc(100%-2rem)] overflow-auto">
            <table className="w-full border-collapse table-auto">
              <thead className="sticky top-0 bg-background border-b z-10">
                <tr>
                  {item.columns && item.columns.map((column) => (
                    <th
                      key={column}
                      className="p-1 text-left font-medium text-[10px] bg-muted/50 whitespace-nowrap"
                    >
                      {column}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {item.data && item.data.map((row, i) => (
                  <tr key={i} className="border-b hover:bg-muted/50">
                    {item.columns && item.columns.map((column, j) => (
                      <td key={`${i}-${j}`} className="p-1 text-[10px] whitespace-nowrap">
                        {row[column] !== undefined ? String(row[column]) : ''}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </Card>
    );
  }

  // For Python plot items, render the plot image
  if (item.type === 'pythonplot') {
    return (
      <Card
        className={`w-full h-full overflow-hidden transition-all duration-200
          ${isEditMode ? 'dashboard-card-edit' : 'dashboard-card no-border'}
          ${isDragging ? 'ring-1 ring-primary' : ''}
          ${isResizing ? 'ring-1 ring-primary' : ''}`}
        data-item-type={item.type}
        data-item-id={item.id}
      >
        {isEditMode && (
          <div className="draggable-handle absolute top-0 left-0 w-full h-6 bg-primary/5 flex items-center justify-center cursor-grab rounded-t-md">
            <GripVertical className="h-4 w-4 text-primary" />
          </div>
        )}

        {isEditMode && (
          <div className="absolute top-2 right-2 flex gap-1 z-10 non-draggable">
            <Button
              variant="ghost"
              size="icon"
              className="h-5 w-5 bg-white/90 hover:bg-red-50 hover:text-red-600 border border-red-200/50 hover:border-red-300 transition-all duration-200 rounded-full shadow-sm non-draggable"
              onClick={() => onRemoveItem(item.id)}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        )}

        <div className={`h-full dashboard-item-content p-2 ${isEditMode ? 'pt-8' : ''}`}>
          <div className="text-sm font-medium mb-2">{item.title || 'Python Plot'}</div>
          <div className="w-full h-[calc(100%-2rem)] flex items-center justify-center overflow-hidden">
            {item.plotUrl && (
              <img
                src={item.plotUrl}
                alt={item.title || 'Python Plot'}
                className="max-w-full max-h-full object-contain"
                onError={(e) => {
                  console.error('Error loading plot image');
                  e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2YwZjBmMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5OTkiPkltYWdlIGxvYWQgZXJyb3I8L3RleHQ+PC9zdmc+';
                }}
              />
            )}
          </div>
        </div>
      </Card>
    );
  }

  // For calculator result items, render the CalculatorResultCard
  if (item.type === 'calculator') {
    return (
      <CalculatorResultCard
        item={item as CalculatorResultItem}
        isEditMode={isEditMode}
        onUpdateCalculator={(calculatorId, updates) => {
          onUpdateItem(calculatorId, updates);
        }}
        onRemoveCalculator={(calculatorId) => {
          onRemoveItem(calculatorId);
        }}
        onEdit={(calculatorItem) => {
          // Handle edit if needed
          console.log('Edit calculator result:', calculatorItem);
        }}
        onDelete={(id) => {
          onRemoveItem(id);
        }}
      />
    );
  }

  // For non-chart items (headings, text), use the Card wrapper
  return (
    <Card
      className={`w-full h-full overflow-visible transition-all duration-200
        ${isEditMode ? 'dashboard-card-edit' : 'dashboard-card no-border'}
        ${isDragging ? 'ring-1 ring-primary' : ''}
        ${isResizing ? 'ring-1 ring-primary' : ''}
        ${item.type === 'text' && isEditMode ? 'bg-white dark:bg-gray-900 border border-primary/20 p-0' : item.type === 'text' ? 'bg-transparent dark:bg-transparent border-0 p-0' : ''}`}
      data-item-type={item.type}
      data-item-id={item.id}
      data-is-dragging={isDragging}
      data-is-resizing={isResizing}
      style={{
        position: 'relative',
        zIndex: (isDragging || isResizing) ? 100 : item.type === 'text' ? 10 : 1,
        minHeight: item.type === 'text' ? (item as TextItem).height <= 2 ? '60px' : '120px' : 'auto',
        transform: 'translate3d(0,0,0)',
        willChange: isEditMode ? 'transform, box-shadow' : 'auto'
      }}
      onMouseDown={() => {
        if (isEditMode) {
          window.activeItemId = item.id;
        }
      }}
      onMouseUp={() => {
        if (isEditMode) {
          window.activeItemId = undefined;
        }
      }}
    >
      {isEditMode && (
        <div className="draggable-handle absolute top-0 left-0 w-full h-6 bg-primary/5 flex items-center justify-center cursor-grab rounded-t-md">
          <GripVertical className="h-4 w-4 text-primary" />
        </div>
      )}

      {isEditMode && (
        <div className="absolute top-2 right-2 flex gap-1 z-10 non-draggable">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 bg-white/90 hover:bg-red-50 hover:text-red-600 border border-red-200/50 hover:border-red-300 transition-all duration-200 rounded-full shadow-sm non-draggable"
            onClick={() => onRemoveItem(item.id)}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      )}
      <div className={`h-full dashboard-item-content ${item.type === 'text' ? 'p-0' : 'p-2'}`}>
        {item.type === 'heading' ? (
          <div className="w-full h-full">
            <HeadingCard
              key={item.id}
              heading={item as HeadingItem}
              isEditMode={isEditMode}
              onUpdateHeading={(headingId, updates) => {
                onUpdateItem(headingId, updates);
              }}
              onRemoveHeading={(headingId) => {
                onRemoveItem(headingId);
              }}
            />
          </div>
        ) : item.type === 'text' ? (
          <>
            {/* Use MarkdownRichTextCard for all text items */}
            <MarkdownRichTextCard
              key={item.id}
              textItem={item as TextItem}
              isEditMode={isEditMode}
              onUpdateText={(textId, updates) => {
                if (textId === item.id) {
                  onUpdateItem(textId, updates);
                }
              }}
              onRemoveText={(textId) => {
                onRemoveItem(textId);
              }}
              onToggleFullscreen={() => handleToggleFullscreen(item.id)}
            />

            {/* Enhanced Fullscreen Modal for Text Items */}
            <EnhancedFullscreenModal
              isOpen={!!fullscreenItem && fullscreenItem.id === item.id}
              onClose={() => setFullscreenItem(null)}
              item={fullscreenItem}
              onUpdateItem={onUpdateItem}
              onRefreshItem={onRefreshItem}
              onEditItem={handleEditChart}
              onExportItem={onExportItem}
              onShareItem={onShareItem}
            />
          </>
        ) : null}
      </div>
    </Card>
  );
}, (prevProps, nextProps) => {
  // Skip re-renders during drag operations for items that aren't being dragged
  if (window.isDraggingDashboardItem && window.activeItemId !== nextProps.item.id) {
    return true; // prevent re-render
  }

  // Custom comparison for memo - only re-render if these specific props change
  return (
    prevProps.item.id === nextProps.item.id &&
    prevProps.isEditMode === nextProps.isEditMode &&
    prevProps.item.type === nextProps.item.type &&
    prevProps.isDragging === nextProps.isDragging &&
    prevProps.isResizing === nextProps.isResizing &&
    // For text cards, check content
    (prevProps.item.type === 'text' ?
      (prevProps.item as TextItem).content === (nextProps.item as TextItem).content :
      true) &&
    // For charts, only re-render if title or type changes (not data)
    (prevProps.item.type === 'chart' ?
      (prevProps.item as SavedChart).title === (nextProps.item as SavedChart).title &&
      (prevProps.item as SavedChart).chartType === (nextProps.item as SavedChart).chartType :
      true)
  );
});

// Add this code near the top of the component where the other variables are defined
const initialCols = 12; // This is the default grid width for desktop
const initialRowHeight = 50; // Default row height in pixels
const initialBreakpoints = { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 };
const initialCols2 = { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 };

export function DashboardLayout({
  items,
  onUpdateItem,
  onRemoveItem,
  onSaveLayout,
  isEditMode = false,
  viewMode = 'grid',
  onRefreshItem,
  onExportItem,
  onShareItem
}: DashboardLayoutProps) {
  // Memoize unique items to prevent unnecessary re-renders and infinite loops
  const uniqueItems = useMemo(() =>
    items.filter((item, index, arr) =>
      arr.findIndex(i => i.id === item.id) === index
    ), [items]
  );

  const [layout, setLayout] = useState<Layout[]>([])
  const [mounted, setMounted] = useState<boolean>(false)
  const [containerWidth, setContainerWidth] = useState<number>(1200)
  const [isDragging, setIsDragging] = useState<boolean>(false)
  const [isResizing, setIsResizing] = useState<boolean>(false)
  const [activeDragItemId, setActiveDragItemId] = useState<string | null>(null)

  // Use refs to store the latest state without triggering re-renders
  const isDraggingRef = useRef(false);
  const isResizingRef = useRef(false);
  const layoutBeforeDragRef = useRef<Layout[]>([]);
  const dragTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update container width when window resizes - debounced for performance
  const updateWidth = useCallback(() => {
    try {
      // Calculate an appropriate width based on parent container
      const dashboardContainer = document.querySelector('.dashboard-container');
      const width = dashboardContainer
        ? dashboardContainer.clientWidth - 32 // Account for padding
        : Math.max(window.innerWidth - 64, 800);

      setContainerWidth(width);
    } catch (error) {
      console.error('Error updating container width:', error);
      // Fallback to a reasonable default
      setContainerWidth(Math.max(window.innerWidth - 64, 800));
    }
  }, []);

  // Add resize observer for more responsive layout updates
  useEffect(() => {
    const dashboardContainer = document.querySelector('.dashboard-container');
    if (dashboardContainer) {
      const resizeObserver = new ResizeObserver(updateWidth);
      resizeObserver.observe(dashboardContainer);

      return () => {
        resizeObserver.disconnect();
      };
    }
    return undefined;
  }, [updateWidth]);

  // Initialize and set up event listeners
  useEffect(() => {
    // Initialize
    setMounted(true);
    updateWidth();

    // Add window resize handler with debounce
    let resizeTimer: NodeJS.Timeout | null = null;

    const handleResize = () => {
      if (resizeTimer) {
        clearTimeout(resizeTimer);
      }
      resizeTimer = setTimeout(updateWidth, 100);
    };

    window.addEventListener('resize', handleResize);

    // Add document event listeners for mouse events
    document.addEventListener('mouseup', handleGlobalMouseUp);
    document.addEventListener('mousemove', handleGlobalMouseMove);

    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.removeEventListener('mousemove', handleGlobalMouseMove);

      if (resizeTimer) {
        clearTimeout(resizeTimer);
      }

      if (dragTimeoutRef.current) {
        clearTimeout(dragTimeoutRef.current);
      }

      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, [updateWidth]);

  // Global mouse event handlers
  const handleGlobalMouseUp = useCallback(() => {
    if (isDraggingRef.current || isResizingRef.current) {
      // Add a delay before clearing state to ensure all React Grid Layout events complete
      if (dragTimeoutRef.current) {
        clearTimeout(dragTimeoutRef.current);
      }

      dragTimeoutRef.current = setTimeout(() => {
        document.body.classList.remove('dragging');
        isDraggingRef.current = false;
        isResizingRef.current = false;
        setIsDragging(false);
        setIsResizing(false);
        setActiveDragItemId(null);

        // Clean up any position indicators
        document.querySelectorAll('.grid-guideline').forEach(el => el.remove());
        document.querySelectorAll('.will-move').forEach(el => el.classList.remove('will-move'));
      }, 200);
    }
  }, []);

  const handleGlobalMouseMove = useCallback((e: MouseEvent) => {
    if (isDraggingRef.current && activeDragItemId) {
      // Could add custom drag visualization here if needed
    }
  }, [activeDragItemId]);

  // When items change, update the layout
  useEffect(() => {
    // Skip if not mounted yet or if dragging/resizing is in progress
    if (!mounted || isDraggingRef.current || isResizingRef.current) {
      return;
    }

    // Convert items to layout format with enhanced position preservation
    try {
      const newLayout: Layout[] = uniqueItems.map(item => {
        // Ensure grid positions are properly preserved
        const gridColumn = typeof item.gridColumn === 'number' ? item.gridColumn : 0;
        const gridRow = typeof item.gridRow === 'number' ? item.gridRow : 0;
        const width = typeof item.width === 'number' ? item.width :
          (item.type === 'heading' ? 12 : item.type === 'calculator' ? 3 : item.type === 'text' ? 6 : 4);
        const height = typeof item.height === 'number' ? item.height :
          (item.type === 'heading' ? 2 : item.type === 'calculator' ? 6 : item.type === 'text' ? 8 : 6);

        return {
          i: item.id,
          x: gridColumn,
          y: gridRow,
          w: width,
          h: height,
          minW: item.type === 'heading' ? 6 : item.type === 'calculator' ? 2 : 2,
          maxW: item.type === 'heading' ? 12 : 12,
          minH: item.type === 'text' ? 2 : item.type === 'calculator' ? 4 : item.type === 'heading' ? 1 : 4,
          maxH: item.type === 'heading' ? 4 : 24,
          isDraggable: isEditMode,
          isResizable: isEditMode,
          isBounded: false, // Allow dragging outside bounds for flexibility
          resizeHandles: ['se'],
          className: item.type === 'text' ? 'text-card' : undefined
        } as Layout;
      });

      setLayout(newLayout);
    } catch (error) {
      console.error('Error updating layout:', error);
    }
  }, [uniqueItems, mounted, isEditMode]);

  // Handle drag start
  const handleDragStart = useCallback((layout: Layout[], oldItem: Layout, newItem: Layout, placeholder: Layout, e: MouseEvent, element: HTMLElement) => {
    try {
      // Check if the click was on a non-draggable element
      const target = e.target as HTMLElement;
      if (
        target.closest('.non-draggable') ||
        target.closest('button') ||
        target.closest('a') ||
        target.closest('input') ||
        target.closest('textarea') ||
        target.closest('.react-resizable-handle') ||
        target.closest('.editing-content')
      ) {
        // Prevent drag if clicking on non-draggable element
        return;
      }

      // Store current layout before changes
      layoutBeforeDragRef.current = layout;

      // Update state
      setIsDragging(true);
      isDraggingRef.current = true;
      setActiveDragItemId(oldItem.i);

      // Add a class to the body to change cursor globally and enable auto-scrolling
      document.body.classList.add('dragging');
      document.body.classList.add('dragging-dashboard-item');

      // Find the item being dragged
      const draggedItem = items.find(item => item.id === oldItem.i);
      if (draggedItem && draggedItem.type === 'chart') {
        // Add a specific class for chart dragging
        element.classList.add('chart-dragging');
      }
      
      // Set up auto-scrolling during drag with improved responsiveness
      let scrollInterval: number | null = null;
      const scrollThreshold = 150; // Larger threshold for easier triggering
      
      const handleDragMove = (e: MouseEvent) => {
        const container = document.querySelector('.dashboard-wrapper') as HTMLElement;
        if (!container) return;

        const containerRect = container.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        
        // Calculate distance from edges to determine scroll speed (dynamic speed based on proximity)
        const topDistance = e.clientY - containerRect.top;
        const bottomDistance = viewportHeight - e.clientY;
        
        // Auto-scroll when near the top or bottom edges with variable speed
        if (topDistance < scrollThreshold) {
          // Near top edge - scroll up with variable speed
          if (scrollInterval !== null) {
            clearInterval(scrollInterval);
          }
          // Calculate speed based on proximity to edge (closer = faster)
          const speed = Math.max(5, Math.round((scrollThreshold - topDistance) / 5));
          scrollInterval = window.setInterval(() => {
            container.scrollBy(0, -speed);
          }, 16) as unknown as number;
        } else if (bottomDistance < scrollThreshold) {
          // Near bottom edge - scroll down with variable speed
          if (scrollInterval !== null) {
            clearInterval(scrollInterval);
          }
          // Calculate speed based on proximity to edge (closer = faster)
          const speed = Math.max(5, Math.round((scrollThreshold - bottomDistance) / 5));
          scrollInterval = window.setInterval(() => {
            container.scrollBy(0, speed);
          }, 16) as unknown as number;
        } else if (scrollInterval !== null) {
          // Not near edges - stop scrolling
          clearInterval(scrollInterval);
          scrollInterval = null;
        }
      };

      // Add mousemove event listener for auto-scrolling
      document.addEventListener('mousemove', handleDragMove);

      // Store cleanup function in window for access in handleDragStop
      (window as any).dragScrollCleanup = () => {
        document.removeEventListener('mousemove', handleDragMove);
        if (scrollInterval !== null) {
          clearInterval(scrollInterval);
          scrollInterval = null;
        }
      };
    } catch (error) {
      console.error('Drag start error:', error);
    }
  }, [items]);

  // Smart layout compaction function - like Databricks/Superset
  const compactLayout = useCallback((layout: Layout[], draggedItem?: Layout) => {
    // Create a copy to avoid mutations
    const compactedLayout = [...layout];

    // Sort by Y position, then X position for consistent processing
    compactedLayout.sort((a, b) => {
      if (a.y === b.y) return a.x - b.x;
      return a.y - b.y;
    });

    // For each item, find the highest possible position without collision
    compactedLayout.forEach((item, index) => {
      if (draggedItem && item.i === draggedItem.i) {
        // Don't compact the item being dragged
        return;
      }

      let newY = 0;
      let foundPosition = false;

      // Try each row starting from the top
      while (!foundPosition && newY < 100) { // Reasonable limit
        const testItem = { ...item, y: newY };

        // Check for collisions with items above this one in the sorted array
        let hasCollision = false;
        for (let i = 0; i < index; i++) {
          const otherItem = compactedLayout[i];
          if (otherItem.i === item.i) continue;

          // Check if items overlap
          if (
            testItem.x < otherItem.x + otherItem.w &&
            testItem.x + testItem.w > otherItem.x &&
            testItem.y < otherItem.y + otherItem.h &&
            testItem.y + testItem.h > otherItem.y
          ) {
            hasCollision = true;
            break;
          }
        }

        if (!hasCollision) {
          item.y = newY;
          foundPosition = true;
        } else {
          newY++;
        }
      }
    });

    return compactedLayout;
  }, []);

  // Handle drag stop
  const handleDragStop = useCallback((layout: Layout[], oldItem: Layout, newItem: Layout, placeholder: Layout, e: MouseEvent, element: HTMLElement) => {
    try {
      // Clean up any classes
      document.body.classList.remove('dragging');
      document.body.classList.remove('dragging-dashboard-item');
      element.classList.remove('chart-dragging');

      // Clean up auto-scroll event listeners and intervals
      if ((window as any).dragScrollCleanup) {
        (window as any).dragScrollCleanup();
        (window as any).dragScrollCleanup = undefined;
      }

      // Remove all guidelines and visual indicators
      document.querySelectorAll('.grid-guideline').forEach(el => el.remove());
      document.querySelectorAll('.will-move').forEach(el => el.classList.remove('will-move'));

      // Calculate position changes
      const oldX = oldItem.x;
      const oldY = oldItem.y;
      const newX = newItem.x;
      const newY = newItem.y;

      // Only save layout if position actually changed
      if (oldX !== newX || oldY !== newY) {
        // Apply smart compaction to minimize gaps while preserving user intent
        const smartLayout = compactLayout(layout, newItem);

        // Convert layout to items with precise position tracking
        const updatedItems = uniqueItems.map(item => {
          const layoutItem = smartLayout.find(l => l.i === item.id);
          if (layoutItem) {
            // Ensure exact position preservation
            return {
              ...item,
              gridColumn: Math.max(0, layoutItem.x), // Ensure non-negative
              gridRow: Math.max(0, layoutItem.y),    // Ensure non-negative
              width: Math.max(1, layoutItem.w),      // Ensure minimum width
              height: Math.max(1, layoutItem.h)      // Ensure minimum height
            };
          }
          return item;
        });

        // Update the layout state immediately for visual feedback
        setLayout(smartLayout);

        // Call save layout function from parent with debouncing
        if (onSaveLayout) {
          // Clear any existing timeout to prevent multiple rapid saves
          if (window.saveLayoutTimeout) {
            clearTimeout(window.saveLayoutTimeout);
          }

          // Debounce the save operation
          window.saveLayoutTimeout = setTimeout(() => {
            onSaveLayout(updatedItems);
          }, 150) as unknown as number;
        }
      }

      // Update state with a delay to avoid conflicts
      setTimeout(() => {
        setIsDragging(false);
        isDraggingRef.current = false;
        setActiveDragItemId(null);
      }, 100);
    } catch (error) {
      console.error('Drag stop error:', error);
      setIsDragging(false);
      isDraggingRef.current = false;
      setActiveDragItemId(null);
    }
  }, [uniqueItems, onSaveLayout, compactLayout]);

  // Handle resize start
  const handleResizeStart = useCallback((layout: Layout[], oldItem: Layout, newItem: Layout, placeholder: Layout, e: MouseEvent, element: HTMLElement) => {
    try {
      // Store current layout
      layoutBeforeDragRef.current = layout;

      // Update state
      setIsResizing(true);
      isResizingRef.current = true;
      setActiveDragItemId(oldItem.i);

      // Add a class to the body
      document.body.classList.add('resizing');
    } catch (error) {
      console.error('Resize start error:', error);
    }
  }, []);

  // Handle resize stop
  const handleResizeStop = useCallback((layout: Layout[], oldItem: Layout, newItem: Layout, placeholder: Layout, e: MouseEvent, element: HTMLElement) => {
    try {
      // Clean up
      document.body.classList.remove('resizing');

      // Calculate size changes
      const oldW = oldItem.w;
      const oldH = oldItem.h;
      const newW = newItem.w;
      const newH = newItem.h;

      // Only save if size actually changed
      if (oldW !== newW || oldH !== newH) {
        // Convert layout to items with precise size tracking
        const updatedItems = uniqueItems.map(item => {
          const layoutItem = layout.find(l => l.i === item.id);
          if (layoutItem) {
            // Ensure exact size and position preservation
            return {
              ...item,
              gridColumn: Math.max(0, layoutItem.x), // Ensure non-negative
              gridRow: Math.max(0, layoutItem.y),    // Ensure non-negative
              width: Math.max(1, layoutItem.w),      // Ensure minimum width
              height: Math.max(1, layoutItem.h)      // Ensure minimum height
            };
          }
          return item;
        });

        // Call save layout function with debouncing
        if (onSaveLayout) {
          // Clear any existing timeout to prevent multiple rapid saves
          if (window.saveLayoutTimeout) {
            clearTimeout(window.saveLayoutTimeout);
          }

          // Debounce the save operation
          window.saveLayoutTimeout = setTimeout(() => {
            onSaveLayout(updatedItems);
          }, 150) as unknown as number;
        }
      }

      // Update state with delay
      setTimeout(() => {
        setIsResizing(false);
        isResizingRef.current = false;
        setActiveDragItemId(null);
      }, 100);
    } catch (error) {
      console.error('Resize stop error:', error);
      setIsResizing(false);
      isResizingRef.current = false;
      setActiveDragItemId(null);
    }
  }, [items, onSaveLayout]);

  // Handle layout change
  const handleLayoutChange = (newLayout: Layout[]) => {
    try {
      // Skip if dragging or resizing is happening
      if (isDraggingRef.current || isResizingRef.current) {
        // Do not update layout during active interactions
        return;
      }

      // Just update the layout state
      setLayout(newLayout);
    } catch (error) {
      console.error('Layout change error:', error);
    }
  };

  // Render the grid layout
  return (
    <div className={`layout ${viewMode}`}
      data-is-editing={isEditMode ? 'true' : 'false'}
      data-view-mode={viewMode}
      data-is-dragging={isDragging ? 'true' : 'false'}
      data-is-resizing={isResizing ? 'true' : 'false'}
    >
      {mounted && (
        <GridLayout
          className={`react-grid-layout ${isDragging ? 'dragging-active' : ''} ${isResizing ? 'resizing-active' : ''}`}
          layout={layout}
          cols={12}
          rowHeight={30} // Reduced row height for finer vertical control
          width={containerWidth}
          isDraggable={isEditMode}
          isResizable={isEditMode}
          onLayoutChange={handleLayoutChange}
          onDragStart={handleDragStart}
          onDragStop={handleDragStop}
          onResizeStart={handleResizeStart}
          onResizeStop={handleResizeStop}
          margin={[8, 8]} // Reduced margin for tighter grid
          containerPadding={[0, 0]}
          useCSSTransforms={false} // Disable CSS transforms for more precise positioning
          transformScale={1}
          preventCollision={true} // Prevent overlapping during drag
          compactType={null} // Disable auto-compaction - we handle this manually
          verticalCompact={false} // Disable vertical compaction - we handle this manually
          // Enhanced drag feedback with smart positioning preview
          onDrag={(layout, oldItem, newItem, placeholder, e, element) => {
            // Highlight the area where the card will be placed
            const placeholderEl = document.querySelector('.react-grid-placeholder');
            if (placeholderEl) {
              placeholderEl.classList.add('active-placeholder');
            }

            // Preview smart compaction during drag
            const previewLayout = compactLayout(layout, newItem);

            // Add visual indicators for items that will be affected
            previewLayout.forEach(item => {
              const originalItem = layout.find(l => l.i === item.i);
              if (originalItem && (originalItem.y !== item.y || originalItem.x !== item.x)) {
                const element = document.querySelector(`[data-grid="${item.i}"]`);
                if (element) {
                  element.classList.add('will-move');
                }
              }
            });
          }}
          isBounded={false} // Allow items to be dragged outside bounds temporarily
          draggableHandle=".draggable-handle" // Only allow dragging from the handle
          draggableCancel=".react-resizable-handle,.editing-content,.non-draggable"
          resizeHandles={['se']} // Only show southeast corner handle for clean UI
        >
          {/* Render each item from the layout */}
          {layout
            .filter((layoutItem, index, arr) =>
              // Remove duplicates by ensuring this is the first occurrence of this ID
              arr.findIndex(item => item.i === layoutItem.i) === index
            )
            .map(layoutItem => {
            const item = uniqueItems.find(i => i.id === layoutItem.i);
            if (!item) return null;

            return (
              <div key={layoutItem.i}
                className={`dashboard-item-container ${isDragging && activeDragItemId === layoutItem.i ? 'dragging' : ''} ${isResizing && activeDragItemId === layoutItem.i ? 'resizing' : ''}`}
                data-grid={layoutItem}
                data-item-id={layoutItem.i}
                data-item-type={item.type}
              >
                <DashboardItemCard
                  key={item.id}
                  item={item}
                  isEditMode={isEditMode}
                  onUpdateItem={onUpdateItem}
                  onRemoveItem={onRemoveItem}
                  onRefreshItem={onRefreshItem}
                  onExportItem={onExportItem}
                  onShareItem={onShareItem}
                  isDragging={isDragging && activeDragItemId === layoutItem.i}
                  isResizing={isResizing && activeDragItemId === layoutItem.i}
                />
              </div>
            );
          })}
        </GridLayout>
      )}
    </div>
  );
}
