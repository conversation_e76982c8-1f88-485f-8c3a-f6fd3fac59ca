'use client'

import { Database, Clock } from "lucide-react"
import { formatDistanceToNow } from 'date-fns'
import { Dataset } from '@/types/index'

interface ServerStatus {
  status: 'healthy' | 'error' | 'warning'
  message: string
}

interface CellStatusProps {
  selectedDatasets: Dataset[]
  cellType: 'code' | 'markdown'
  executionTime: {
    startTime?: Date;
    endTime?: Date;
  }
  isRunning: boolean
  currentExecutionTime: number
}

export function CellStatus({
  selectedDatasets,
  cellType,
  executionTime,
  isRunning,
  currentExecutionTime
}: CellStatusProps) {
  return (
    <>
      {/* Dataset Info - Even more compact */}
      {(selectedDatasets.length > 0 || cellType === 'code') && (
        <div className="px-1 pt-0 pb-0 text-[9px] text-muted-foreground flex items-center justify-between gap-1">
          <div className="flex items-center gap-1">
            <Database className="h-3 w-3" />
            <span className="truncate max-w-[120px]">
              {selectedDatasets.length === 0 
                ? 'No datasets selected'
                : selectedDatasets.length === 1
                  ? selectedDatasets[0].name
                  : `${selectedDatasets.length} datasets`
              }
            </span>
          </div>
          
          {/* Execution Time - Compact */}
          {(executionTime.startTime || isRunning) && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span className="text-[8px]">
                {isRunning ? (
                  <span className="text-blue-600">
                    Running... {(currentExecutionTime / 1000).toFixed(1)}s
                  </span>
                ) : executionTime.endTime ? (
                  <>
                    {formatDistanceToNow(executionTime.endTime, { addSuffix: true })}
                    {executionTime.startTime && executionTime.endTime && (
                      <span className="ml-1">
                        ({((executionTime.endTime.getTime() - executionTime.startTime.getTime()) / 1000).toFixed(2)}s)
                      </span>
                    )}
                  </>
                ) : (
                  'Starting...'
                )}
              </span>
            </div>
          )}
        </div>
      )}
    </>
  )
}
