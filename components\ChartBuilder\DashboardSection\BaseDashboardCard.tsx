'use client'

import { useState, useRef, useEffect, useCallback, ReactNode } from 'react';
import { GripVertical, Maximize2, Trash2, Edit3, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { cn } from '@/lib/utils';
import { DashboardItem } from './types';

export interface BaseDashboardCardProps {
  item: DashboardItem;
  isEditMode: boolean;
  onUpdateItem: (itemId: string, updates: Partial<DashboardItem>) => void;
  onRemoveItem: (itemId: string) => void;
  onToggleFullscreen: (itemId: string) => void;
  onRefreshItem?: (itemId: string) => void;
  onEditItem?: (itemId: string) => void;
  children: ReactNode;
  title?: string;
  subtitle?: string;
  description?: string;
  icon?: ReactNode;
  headerActions?: ReactNode;
  className?: string;
  contentClassName?: string;
  showRefresh?: boolean;
  showEdit?: boolean;
}

export function BaseDashboardCard({
  item,
  isEditMode,
  onUpdateItem,
  onRemoveItem,
  onToggleFullscreen,
  onRefreshItem,
  onEditItem,
  children,
  title,
  subtitle,
  description,
  icon,
  headerActions,
  className,
  contentClassName,
  showRefresh = false,
  showEdit = false
}: BaseDashboardCardProps) {
  const [isDragging, setIsDragging] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Function to trigger window resize to ensure content redraw properly
  const triggerResize = useCallback(() => {
    // Prevent infinite loops by checking if we're already in a resize operation
    if (typeof window !== 'undefined' && !window.isResizingDashboardItem && !window.dashboardResizeInProgress) {
      window.dashboardResizeInProgress = true;
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
        window.dashboardResizeInProgress = false;
      }, 0);
    }
  }, []);

  // Enhanced resize handling
  useEffect(() => {
    let mounted = true;
    let resizeTimeout: NodeJS.Timeout;

    // Initial resize with a single delayed call
    setTimeout(() => {
      if (mounted && typeof window !== 'undefined') {
        window.dispatchEvent(new Event('resize'));
      }
    }, 100);

    // Throttled resize handler that doesn't call triggerResize to prevent loops
    const handleResize = () => {
      if (!mounted || typeof window === 'undefined') return;

      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        if (mounted) {
          // Force chart/content redraw without triggering more resize events
          const event = new CustomEvent('dashboard-content-refresh', {
            detail: { itemId: item.id }
          });
          window.dispatchEvent(event);
        }
      }, 100);
    };

    // Custom event listener for dashboard item resizes (but don't trigger more resizes)
    const handleDashboardResize = (event: CustomEvent) => {
      if (mounted && event.detail?.itemId === item.id) {
        // Just refresh content, don't trigger more resize events
        setTimeout(() => {
          if (mounted && typeof window !== 'undefined') {
            const refreshEvent = new CustomEvent('dashboard-content-refresh', {
              detail: { itemId: item.id }
            });
            window.dispatchEvent(refreshEvent);
          }
        }, 50);
      }
    };

    // Only add listeners if window is available
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
      window.addEventListener('dashboard-item-resized', handleDashboardResize as EventListener);

      return () => {
        mounted = false;
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('dashboard-item-resized', handleDashboardResize as EventListener);
        clearTimeout(resizeTimeout);
      };
    }

    return () => {
      mounted = false;
      clearTimeout(resizeTimeout);
    };
  }, [item.id]);

  // Resize handle component
  const ResizeHandle = ({ direction }: { direction: 'e' | 's' | 'se' }) => {
    const handleMouseDown = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      if (typeof window !== 'undefined') {
        window.isResizingDashboardItem = true;
      }

      const startX = e.clientX;
      const startY = e.clientY;
      const startWidth = item.width;
      const startHeight = item.height;

      const handleMouseMove = (e: MouseEvent) => {
        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;

        let newWidth = startWidth;
        let newHeight = startHeight;

        if (direction === 'e' || direction === 'se') {
          newWidth = Math.max(1, startWidth + Math.round(deltaX / 100));
        }

        if (direction === 's' || direction === 'se') {
          newHeight = Math.max(2, startHeight + Math.round(deltaY / 100));
        }

        onUpdateItem(item.id, { width: newWidth, height: newHeight });
      };

      const handleMouseUp = () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);

        if (typeof window !== 'undefined') {
          window.isResizingDashboardItem = false;
        }

        // Single resize trigger after a short delay to allow DOM to update
        setTimeout(() => {
          triggerResize();

          // Dispatch custom event for other components to react
          if (typeof window !== 'undefined' && window.dispatchEvent) {
            window.dispatchEvent(new CustomEvent('dashboard-item-resized', {
              detail: { itemId: item.id, width: item.width, height: item.height }
            }));
          }
        }, 50);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };

    return (
      <div
        className={`absolute ${direction === 'e' ? 'right-0 top-0 bottom-0 w-2 cursor-ew-resize' :
                                direction === 's' ? 'bottom-0 left-0 right-0 h-2 cursor-ns-resize' :
                                'bottom-0 right-0 w-4 h-4 cursor-nwse-resize'}`}
        style={{
          backgroundColor: 'rgba(0, 0, 0, 0.05)',
          zIndex: 10,
          borderRadius: direction === 'se' ? '0 0 4px 0' : '0'
        }}
        onMouseDown={handleMouseDown}
      />
    );
  };

  // Drag handlers
  const handleDragStart = useCallback(() => {
    setIsDragging(true);
    if (typeof window !== 'undefined') {
      window.isDraggingDashboardItem = true;
    }
  }, []);

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
    if (typeof window !== 'undefined') {
      window.isDraggingDashboardItem = false;
    }
    triggerResize();
  }, [triggerResize]);

  useEffect(() => {
    const cardElement = cardRef.current;
    if (!cardElement) return;

    cardElement.addEventListener('dragstart', handleDragStart);
    cardElement.addEventListener('dragend', handleDragEnd);

    return () => {
      cardElement.removeEventListener('dragstart', handleDragStart);
      cardElement.removeEventListener('dragend', handleDragEnd);
    };
  }, [handleDragStart, handleDragEnd]);

  return (
    <div
      ref={cardRef}
      className={cn(
        "relative rounded-lg bg-card text-card-foreground dashboard-card-group",
        isDragging ? "z-50 cursor-grabbing" : isEditMode ? "cursor-grab" : "",
        isEditMode ? "border shadow-sm hover:border-primary" : "border-0 shadow-none",
        "dashboard-item-container",
        !isEditMode && "no-border",
        className
      )}
      data-item-type={item.type}
      data-item-id={item.id}
      data-is-dragging={isDragging}
      data-is-resizing={false}
      style={{
        width: '100%',
        height: '100%',
        display: 'grid',
        gridTemplateRows: description ? '24px 1fr 20px' : '24px 1fr',
        overflow: 'hidden',
        transform: 'translate3d(0,0,0)',
        willChange: isEditMode ? 'transform, box-shadow' : 'auto'
      }}
    >
      {/* Draggable Handle - Positioned at the top */}
      {isEditMode && (
        <div className="draggable-handle absolute top-0 left-0 w-full h-6 flex items-center justify-center cursor-grab rounded-t-md z-10 bg-muted/50">
          <GripVertical className="h-4 w-4 text-primary" />
        </div>
      )}

      {/* Card Header */}
      <div className={`flex items-center justify-between px-1 py-0.5 ${isEditMode ? 'border-b border-border' : ''} bg-card`}>
        <div className="flex items-center gap-1">
          {icon && <div className="flex items-center">{icon}</div>}
          <h3 className="text-xs font-medium truncate text-foreground">{title}</h3>
          {subtitle && (
            <span className="text-[10px] text-muted-foreground bg-muted/20 px-1 py-0.5 rounded ml-1">
              {subtitle}
            </span>
          )}
        </div>
        <div className="flex items-center gap-1">
          {showRefresh && onRefreshItem && (
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0 hover:bg-green-100 hover:text-green-600 transition-colors non-draggable"
              onClick={() => onRefreshItem(item.id)}
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          )}
          {showEdit && (
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0 hover:bg-blue-100 hover:text-blue-600 transition-colors non-draggable"
              onClick={() => onEditItem?.(item.id)}
            >
              <Edit3 className="h-3 w-3" />
            </Button>
          )}
          {isEditMode && (
            <>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 hover:bg-blue-100 hover:text-blue-600 transition-colors non-draggable"
                onClick={() => onToggleFullscreen(item.id)}
              >
                <Maximize2 className="h-3 w-3" />
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 w-5 p-0 hover:bg-red-100 hover:text-red-600 transition-colors non-draggable"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Remove {item.type}</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to remove this {item.type}? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={() => onRemoveItem(item.id)}>
                      Remove
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </>
          )}
          {headerActions}
        </div>
      </div>

      {/* Content Area */}
      <div
        ref={contentRef}
        className={cn("w-full h-full overflow-hidden", contentClassName)}
        style={{
          marginTop: isEditMode ? '20px' : '0',
          minHeight: '100px'
        }}
      >
        {children}
      </div>

      {/* Footer - Only shown if there's a description */}
      {description && (
        <div className={`${isEditMode ? 'border-t dark:border-gray-700' : ''} dark:bg-black bg-white text-[10px] text-muted-foreground overflow-hidden`}>
          <p className="truncate px-1">{description}</p>
        </div>
      )}

      {/* Resize Handles */}
      {isEditMode && (
        <>
          <ResizeHandle direction="e" />
          <ResizeHandle direction="s" />
          <ResizeHandle direction="se" />
        </>
      )}
    </div>
  );
}
