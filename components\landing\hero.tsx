import { <PERSON>R<PERSON>, ArrowRightIcon } from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "../ui/button";
import { motion } from "framer-motion";
import Image from "next/image";
import Container from "./global/container";
import { BlurText } from "../ui/blur-text";
import { SignInButton } from "@clerk/nextjs";
import { SignedIn } from "@clerk/nextjs";
import { SignedOut } from "@clerk/nextjs";
import TrueFocus from "../ui/TrueFocus";
import CardSwap, { Card } from "../ui/CardSwap";



const Hero = () => {
    return (
        <div className="flex flex-col items-center text-center w-full max-w-5xl my-24 mx-auto z-40 relative">
            <Container delay={0.0}>
                <div className="pl-2 pr-1 py-1 rounded-full border border-foreground/10 hover:border-foreground/15 backdrop-blur-lg cursor-pointer flex items-center gap-2.5 select-none w-max mx-auto">
                    <div className="w-3.5 h-3.5 rounded-full bg-primary/40 flex items-center justify-center relative">
                        <div className="w-2.5 h-2.5 rounded-full bg-primary/60 flex items-center justify-center animate-ping">
                            <div className="w-2.5 h-2.5 rounded-full bg-primary/60 flex items-center justify-center animate-ping"></div>
                        </div>
                        <div className="w-1.5 h-1.5 rounded-full bg-primary flex items-center justify-center absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                        </div>
                    </div>
                    <Link href="/changelog" className="inline-flex items-center justify-center gap-2 animate-text-gradient animate-background-shine bg-gradient-to-r from-[#b2a8fd] via-[#8678f9] to-[#c7d2fe] bg-[200%_auto] bg-clip-text text-sm text-transparent hover:opacity-80 transition-opacity">
                        Build for the future
                        <span className="text-xs text-secondary-foreground px-1.5 py-0.5 rounded-full bg-gradient-to-b from-foreground/20 to-foreground/10 flex items-center justify-center">
                            What&apos;s new
                            <ArrowRightIcon className="w-3.5 h-3.5 ml-1 text-foreground/50" />
                        </span>
                    </Link>
                </div>
            </Container>

            
        <div className="mt-6">
        <TrueFocus 
            sentence="Analyze. Automate. Optimize"
            manualMode={true}
            blurAmount={5}
            animationDuration={1}
            pauseBetweenAnimations={1}
            />
        </div>

         <header className=" py-12 px-6 text-center">
            <h1 className="text-4xl font-bold">LoopFlow — Your Smart Business Companion</h1>
            <p className="mt-4 text-lg">
                Automate tasks, manage data, and collaborate effortlessly with AI-powered workflows.
            </p>
        </header>



            {/* <BlurText
                word={"Your ultimate AI Project Management Streamline Workflow"}
                className="text-3xl sm:text-5xl lg:text-6xl xl:text-7xl bg-gradient-to-br from-foreground to-foreground/60 bg-clip-text text-transparent py-2 md:py-0 lg:!leading-snug font-medium racking-[-0.0125em] mt-6 font-heading"
            />
            <Container delay={0.1}>
                <p className="text-sm sm:text-base lg:text-lg mt-4 text-accent-foreground/80 max-w-2xl mx-auto text-center">
                    Elevate your Project Management and HR processes with <span className="font-semibold text-primary">LoopFlow</span>. 
                    <span className="hidden sm:inline">
                        Your all-in-one solution to streamline workflows, enhance productivity, and make smarter decisions effortlessly.
                    </span>
                </p>
            </Container> */}
            <Container delay={0.2}>
                <div className="flex items-center justify-center md:gap-x-6 mt-8">
                    <SignedIn>
                        <Button size="lg" className="text-lg px-8 py-6" asChild>
                            <Link href="/hr/workspace">
                                Dashboard <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                        <Button variant="outline" size="lg" className="text-lg px-8 py-6" asChild>
                            <Link href="#features">Learn More</Link>
                        </Button>
                    </SignedIn>
                    <SignedOut>
                        <SignInButton mode="modal">
                            <Button size="lg" className="text-lg px-8 py-6">
                                Sign In
                            </Button>
                        </SignInButton>
                        <Button variant="outline" size="lg" className="text-lg px-8 py-6" asChild>
                            <Link href="#features">Learn More</Link>
                        </Button>
                    </SignedOut>
                </div>
            </Container>

            <div style={{ height: '600px', position: 'relative' }} className="w-full max-w-4xl mx-auto">
            <CardSwap
                width={600}
                height={500}
                cardDistance={80}
                verticalDistance={70}
                delay={5000}
                pauseOnHover={false}
            >
                <Card>
                    <div className="relative w-full h-full">
                        <Image
                            src="/dashboardBi.png"
                            alt="Dashboard Interface"
                            fill
                            className="object-cover rounded-lg"
                            priority
                        />
                        <div className="absolute bottom-0 left-0 right-0 p-4 bg-black/50 backdrop-blur-sm rounded-b-lg">
                            <h3 className="text-xl font-semibold mb-2 text-white">Smart Dashboard</h3>
                            <p className="text-sm text-gray-200">Powerful analytics and insights at your fingertips</p>
                        </div>
                    </div>
                </Card>
                {/* <Card>
                    <div className="relative w-full h-full">
                        <Image
                            src="/datanotebook.png"
                            alt="Data Notebook"
                            fill
                            className="object-cover rounded-lg"
                            priority
                        />
                        <div className="absolute bottom-0 left-0 right-0 p-4 bg-black/50 backdrop-blur-sm rounded-b-lg">
                            <h3 className="text-xl font-semibold mb-2 text-white">Data Notebook</h3>
                            <p className="text-sm text-gray-200">Advanced data analysis and visualization tools</p>
                        </div>
                    </div>
                </Card> */}
                <Card>
                    <div className="relative w-full h-full">
                        <Image
                            src="/loopflowagent.png"
                            alt="LoopFlow Agent"
                            fill
                            className="object-cover rounded-lg"
                            priority
                        />
                        <div className="absolute bottom-0 left-0 right-0 p-4 bg-black/50 backdrop-blur-sm rounded-b-lg">
                            <h3 className="text-xl font-semibold mb-2 text-white">AI Assistant</h3>
                            <p className="text-sm text-gray-200">Intelligent automation with our AI agent</p>
                        </div>
                    </div>
                </Card>
                <Card>
                    <div className="relative w-full h-full">
                        <Image
                            src="/notebook1.png"
                            alt="LoopFlow Agent"
                            fill
                            className="object-cover rounded-lg"
                            priority
                        />
                        <div className="absolute bottom-0 left-0 right-0 p-4 bg-black/50 backdrop-blur-sm rounded-b-lg">
                            <h3 className="text-xl font-semibold mb-2 text-white">AI Assistant</h3>
                            <p className="text-sm text-gray-200">Intelligent automation with our AI agent</p>
                        </div>
                    </div>
                </Card>
                <Card>
                    <div className="relative w-full h-full">
                        <Image
                            src="/notebookdata.png"
                            alt="LoopFlow Agent"
                            fill
                            className="object-cover rounded-lg"
                            priority
                        />
                        <div className="absolute bottom-0 left-0 right-0 p-4 bg-black/50 backdrop-blur-sm rounded-b-lg">
                            <h3 className="text-xl font-semibold mb-2 text-white">AI Assistant</h3>
                            <p className="text-sm text-gray-200">Intelligent automation with our AI agent</p>
                        </div>
                    </div>
                </Card>
            </CardSwap>
            </div>
        </div>
    )
};

export default Hero
