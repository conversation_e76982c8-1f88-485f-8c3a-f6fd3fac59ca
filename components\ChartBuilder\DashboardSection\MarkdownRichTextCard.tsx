'use client'

/**
 * MarkdownRichTextCard Component
 *
 * A markdown-based rich text editor card for the dashboard.
 * Features:
 * - Markdown editing with preview
 * - Clean separation between edit and view modes
 * - Smart save functionality with explicit "Done" button
 * - Keyboard shortcuts
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { TextItem } from './types';
import { Grip, Trash2, Edit, Bold, Italic, List, ListOrdered, Code, Link, Image as ImageIcon, Table as TableIcon, ChevronDown, Quote, Hash, Minus, Maximize2, Palette } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';
import { useTheme } from 'next-themes';
import { cn } from "@/lib/utils";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import dynamic from 'next/dynamic';

// Dynamically import tldraw with proper configuration
const Tldraw = dynamic(
  () => import('tldraw').then((mod) => ({ default: mod.Tldraw })),
  {
    ssr: false,
    loading: () => (
      <div className="w-full h-64 bg-white rounded-md flex items-center justify-center border">
        <div className="text-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-sm text-gray-600">Loading whiteboard...</p>
        </div>
      </div>
    )
  }
);
import rehypeRaw from 'rehype-raw';
import rehypeHighlight from 'rehype-highlight';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'highlight.js/styles/github.css';
import 'katex/dist/katex.min.css';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Copy, Check, ExternalLink } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover";

// Custom components for enhanced markdown rendering
const CodeBlock = ({ children, className, ...props }: any) => {
  const { theme } = useTheme();
  const [copied, setCopied] = useState(false);
  const match = /language-(\w+)/.exec(className || '');
  const language = match ? match[1] : '';

  const copyToClipboard = () => {
    navigator.clipboard.writeText(children);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  if (language) {
    return (
      <div className="relative group">
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity z-10"
          onClick={copyToClipboard}
        >
          {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
        </Button>
        <SyntaxHighlighter
          style={theme === 'dark' ? oneDark : oneLight}
          language={language}
          PreTag="div"
          className="text-xs rounded-md border"
          {...props}
        >
          {children}
        </SyntaxHighlighter>
      </div>
    );
  }

  return (
    <code className="bg-muted/60 text-muted-foreground rounded px-1.5 py-0.5 text-xs font-mono" {...props}>
      {children}
    </code>
  );
};

const CustomImage = ({ src, alt, ...props }: any) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [proxiedSrc, setProxiedSrc] = useState(src);

  useEffect(() => {
    // Check if it's an external URL that might have CORS issues
    if (src && (src.startsWith('http://') || src.startsWith('https://')) && !src.includes(window.location.hostname)) {
      // Try to use a CORS proxy for external images
      const corsProxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(src)}`;
      setProxiedSrc(corsProxyUrl);
    } else {
      setProxiedSrc(src);
    }
  }, [src]);

  const handleImageError = () => {
    if (proxiedSrc !== src && !imageError) {
      // If proxy failed, try original URL
      setProxiedSrc(src);
      setImageError(false);
    } else {
      setImageError(true);
    }
  };

  return (
    <div className="my-2 rounded-md border overflow-hidden bg-muted/30">
      {imageError ? (
        <div className="flex items-center justify-center p-4 text-muted-foreground">
          <ImageIcon className="h-4 w-4 mr-2" />
          <div className="text-xs">
            <div>Failed to load image: {alt || src}</div>
            <div className="text-[10px] mt-1 opacity-70">
              External images may be blocked by CORS policy
            </div>
          </div>
        </div>
      ) : (
        <img
          src={proxiedSrc}
          alt={alt}
          className="w-full h-auto max-h-96 object-contain"
          onError={handleImageError}
          onLoad={() => setIsLoading(false)}
          style={{ display: isLoading ? 'none' : 'block' }}
          crossOrigin="anonymous"
          {...props}
        />
      )}
      {isLoading && !imageError && (
        <div className="flex items-center justify-center p-4">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span className="ml-2 text-xs text-muted-foreground">Loading image...</span>
        </div>
      )}
    </div>
  );
};

const CustomLink = ({ href, children, ...props }: any) => {
  const isExternal = href?.startsWith('http');

  return (
    <a
      href={href}
      className="text-primary hover:underline inline-flex items-center gap-1"
      target={isExternal ? '_blank' : undefined}
      rel={isExternal ? 'noopener noreferrer' : undefined}
      {...props}
    >
      {children}
      {isExternal && <ExternalLink className="h-3 w-3" />}
    </a>
  );
};

const CustomTable = ({ children, ...props }: any) => (
  <div className="my-2 overflow-x-auto">
    <table className="w-full text-xs border-collapse border border-border rounded-md overflow-hidden" {...props}>
      {children}
    </table>
  </div>
);

const CustomBlockquote = ({ children, ...props }: any) => (
  <blockquote
    className="border-l-4 border-primary bg-muted/30 pl-4 py-2 my-2 rounded-r text-sm italic"
    {...props}
  >
    {children}
  </blockquote>
);

// Custom components for ReactMarkdown
const customComponents = {
  code: CodeBlock,
  img: CustomImage,
  a: CustomLink,
  table: CustomTable,
  blockquote: CustomBlockquote,
  // Enhanced heading components with better spacing
  h1: ({ children, ...props }: any) => (
    <h1 className="text-lg font-semibold text-foreground mb-2 mt-1 border-b border-border pb-1" {...props}>
      {children}
    </h1>
  ),
  h2: ({ children, ...props }: any) => (
    <h2 className="text-base font-semibold text-foreground mb-2 mt-1" {...props}>
      {children}
    </h2>
  ),
  h3: ({ children, ...props }: any) => (
    <h3 className="text-sm font-semibold text-foreground mb-1 mt-1" {...props}>
      {children}
    </h3>
  ),
  // Enhanced list components
  ul: ({ children, ...props }: any) => (
    <ul className="list-disc list-inside text-sm mb-2 space-y-0.5" {...props}>
      {children}
    </ul>
  ),
  ol: ({ children, ...props }: any) => (
    <ol className="list-decimal list-inside text-sm mb-2 space-y-0.5" {...props}>
      {children}
    </ol>
  ),
  li: ({ children, ...props }: any) => (
    <li className="text-sm text-foreground" {...props}>
      {children}
    </li>
  ),
  // Enhanced table components
  th: ({ children, ...props }: any) => (
    <th className="bg-muted/60 border border-border px-2 py-1 text-xs font-medium text-left" {...props}>
      {children}
    </th>
  ),
  td: ({ children, ...props }: any) => (
    <td className="border border-border px-2 py-1 text-xs" {...props}>
      {children}
    </td>
  ),
  // Enhanced paragraph with better spacing
  p: ({ children, ...props }: any) => (
    <p className="text-sm text-foreground leading-snug mb-2" {...props}>
      {children}
    </p>
  ),
};

interface MarkdownRichTextCardProps {
  textItem: TextItem;
  isEditMode: boolean;
  onUpdateText: (textId: string, updates: Partial<TextItem>) => void;
  onRemoveText: (textId: string) => void;
  onToggleFullscreen?: () => void;
  borderless?: boolean; // Add borderless prop for cell context
  hideToolbar?: boolean; // Add hideToolbar prop for cell context
  hideHeader?: boolean; // Add hideHeader prop to hide drag/edit/delete header
  externalIsEditing?: boolean; // External control of editing state
  onEditingChange?: (isEditing: boolean) => void; // Callback when editing state changes
}

// Markdown toolbar component
function MarkdownToolbar({
  onAction,
  setIsToolbarAction,
  onWhiteboardOpen
}: {
  onAction: (action: string) => void,
  setIsToolbarAction: (value: boolean) => void,
  onWhiteboardOpen?: () => void
}) {
  const handleButtonClick = (action: string) => {
    setIsToolbarAction(true);
    onAction(action);
    // Reset after a short delay
    setTimeout(() => setIsToolbarAction(false), 100);
  };

  return (
    <div className="flex items-center gap-0.5">
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-muted/60"
        onClick={() => handleButtonClick('bold')}
        title="Bold"
      >
        <Bold className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-muted/60"
        onClick={() => handleButtonClick('italic')}
        title="Italic"
      >
        <Italic className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-muted/60"
        onClick={() => handleButtonClick('bulletList')}
        title="Bullet List"
      >
        <List className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-muted/60"
        onClick={() => handleButtonClick('numberedList')}
        title="Numbered List"
      >
        <ListOrdered className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-muted/60"
        onClick={() => handleButtonClick('code')}
        title="Code"
      >
        <Code className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-muted/60"
        onClick={() => handleButtonClick('link')}
        title="Link"
      >
        <Link className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-muted/60"
        onClick={() => handleButtonClick('image')}
        title="Image"
      >
        <ImageIcon className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-muted/60"
        onClick={() => handleButtonClick('table')}
        title="Table"
      >
        <TableIcon className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-muted/60"
        onClick={() => handleButtonClick('quote')}
        title="Quote"
      >
        <Quote className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-muted/60"
        onClick={() => handleButtonClick('heading')}
        title="Heading"
      >
        <Hash className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-muted/60"
        onClick={() => handleButtonClick('divider')}
        title="Divider"
      >
        <Minus className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-muted/60"
        onClick={() => onWhiteboardOpen?.()}
        title="Whiteboard"
      >
        <Palette className="h-3 w-3" />
      </Button>
    </div>
  );
}

export function MarkdownRichTextCard({ textItem, isEditMode, onUpdateText, onRemoveText, onToggleFullscreen, borderless = false, hideToolbar = false, hideHeader = false, externalIsEditing, onEditingChange }: MarkdownRichTextCardProps) {
  const { theme } = useTheme();
  
  // State for UI interactions
  const [internalIsEditing, setInternalIsEditing] = useState<boolean>(false);
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [editValue, setEditValue] = useState<string>(textItem.content || '');
  const [isToolbarAction, setIsToolbarAction] = useState(false);
  const [isWhiteboardModalOpen, setIsWhiteboardModalOpen] = useState(false);
  const [currentEditor, setCurrentEditor] = useState<any>(null);

  // Use external editing state if provided, otherwise use internal state
  const isEditing = externalIsEditing !== undefined ? externalIsEditing : internalIsEditing;
  const setIsEditing = (editing: boolean) => {
    if (externalIsEditing !== undefined) {
      onEditingChange?.(editing);
    } else {
      setInternalIsEditing(editing);
    }
  };

  // Refs for managing state and avoiding re-renders
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isNewRef = useRef<boolean>(textItem.isNew || false);
  const isMountedRef = useRef<boolean>(false);
  
  // Initialize once on mount
  useEffect(() => {
    // Cleanup function for any timeouts
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  // Setup initial state once mounted
  useEffect(() => {
    // If new item, enter edit mode after a brief delay
    if (textItem.isNew && !isMountedRef.current) {
      isNewRef.current = true;
      timeoutRef.current = setTimeout(() => {
        setIsEditing(true);
      }, 100);
    }
    
    // Update edit value when content changes from props
    setEditValue(textItem.content || '');
    
    isMountedRef.current = true;
  }, [textItem.id, textItem.isNew, textItem.content]);
  
  // Focus textarea and adjust height when entering edit mode
  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
      // Auto-adjust height based on content
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [isEditing]);
  
  // Handle mouse enter/leave for hover effects
  const handleMouseEnter = () => {
    setIsHovered(true);
  };
  
  const handleMouseLeave = () => {
    setIsHovered(false);
  };
  
  // Handle double click to enter edit mode
  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isEditMode && !isEditing) {
      setIsEditing(true);
    }
  };
  
  // Handle edit button click
  const handleEditClick = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    if (isEditMode && !isEditing) {
      setIsEditing(true);
    }
  };
  
  // Handle textarea change
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditValue(e.target.value);
    
    // Auto-adjust height based on content
    e.target.style.height = "auto";
    e.target.style.height = `${e.target.scrollHeight}px`;
  };
  
  // Handle toolbar actions
  const handleAction = (action: string) => {
    if (!textareaRef.current) return;
    
    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = editValue.substring(start, end);
    
    let newText = editValue;
    let newCursorPos = end;
    
    switch (action) {
      case 'bold':
        newText = editValue.substring(0, start) + `**${selectedText}**` + editValue.substring(end);
        newCursorPos = end + 4;
        break;
      case 'italic':
        newText = editValue.substring(0, start) + `*${selectedText}*` + editValue.substring(end);
        newCursorPos = end + 2;
        break;
      case 'bulletList':
        if (selectedText) {
          const lines = selectedText.split('\n');
          const bulletList = lines.map(line => `- ${line}`).join('\n');
          newText = editValue.substring(0, start) + bulletList + editValue.substring(end);
          newCursorPos = start + bulletList.length;
        } else {
          newText = editValue.substring(0, start) + '- ' + editValue.substring(end);
          newCursorPos = start + 2;
        }
        break;
      case 'numberedList':
        if (selectedText) {
          const lines = selectedText.split('\n');
          const numberedList = lines.map((line, i) => `${i + 1}. ${line}`).join('\n');
          newText = editValue.substring(0, start) + numberedList + editValue.substring(end);
          newCursorPos = start + numberedList.length;
        } else {
          newText = editValue.substring(0, start) + '1. ' + editValue.substring(end);
          newCursorPos = start + 3;
        }
        break;
      case 'code':
        if (selectedText.includes('\n')) {
          newText = editValue.substring(0, start) + '```\n' + selectedText + '\n```' + editValue.substring(end);
          newCursorPos = end + 8;
        } else {
          newText = editValue.substring(0, start) + '`' + selectedText + '`' + editValue.substring(end);
          newCursorPos = end + 2;
        }
        break;
      case 'link':
        newText = editValue.substring(0, start) + `[${selectedText || 'Link text'}](url)` + editValue.substring(end);
        newCursorPos = start + (selectedText ? selectedText.length + 3 : 11);
        break;
      case 'image':
        newText = editValue.substring(0, start) + `![${selectedText || 'Alt text'}](url)` + editValue.substring(end);
        newCursorPos = start + (selectedText ? selectedText.length + 4 : 12);
        break;
      case 'table':
        newText = editValue.substring(0, start) +
          '| Header 1 | Header 2 | Header 3 |\n' +
          '| -------- | -------- | -------- |\n' +
          '| Cell 1   | Cell 2   | Cell 3   |\n' +
          '| Cell 4   | Cell 5   | Cell 6   |' +
          editValue.substring(end);
        newCursorPos = start + 129;
        break;
      case 'quote':
        if (selectedText) {
          const lines = selectedText.split('\n');
          const quotedText = lines.map(line => `> ${line}`).join('\n');
          newText = editValue.substring(0, start) + quotedText + editValue.substring(end);
          newCursorPos = start + quotedText.length;
        } else {
          newText = editValue.substring(0, start) + '> ' + editValue.substring(end);
          newCursorPos = start + 2;
        }
        break;
      case 'heading':
        newText = editValue.substring(0, start) + `# ${selectedText || 'Heading'}` + editValue.substring(end);
        newCursorPos = start + (selectedText ? selectedText.length + 2 : 9);
        break;
      case 'divider':
        newText = editValue.substring(0, start) + '\n---\n' + editValue.substring(end);
        newCursorPos = start + 5;
        break;
    }
    
    setEditValue(newText);
    
    // Set cursor position after state update
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
        
        // Auto-adjust height based on content
        textareaRef.current.style.height = "auto";
        textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
      }
    }, 0);
  };
  
  // Handle save button click
  const handleSave = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    
    try {
      // Exit edit mode first
      setIsEditing(false);
      
      // Only save if content changed or it's a new item
      if (editValue !== textItem.content || isNewRef.current) {
        // Use timeout to avoid update cycles
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        
        timeoutRef.current = setTimeout(() => {
          onUpdateText(textItem.id, {
            content: editValue,
            isNew: false,
            isRichText: true
          });
          
          isNewRef.current = false;
          
          toast.success('Markdown saved', { duration: 2000 });
        }, 100);
      }
    } catch (error) {
      console.error('Error saving markdown:', error);
      toast.error('Failed to save markdown');
      setIsEditing(false);
    }
  };
  
  // Handle blur event
  const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    // Only save on blur if it's not a toolbar action
    if (!isToolbarAction) {
      handleSave();
    }
  };
  
  // Cancel editing
  const handleCancel = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }

    setIsEditing(false);
    // Restore original content from props
    setEditValue(textItem.content || '');
  };

  // Handle whiteboard integration
  const handleWhiteboardClose = useCallback(async (editor?: any) => {
    console.log('🎨 Saving whiteboard...');

    try {
      if (editor) {
        // Export the current drawing as SVG
        const svg = await editor.getSvg();
        if (svg) {
          // Convert SVG to data URL
          const svgData = new XMLSerializer().serializeToString(svg);
          const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
          const svgUrl = URL.createObjectURL(svgBlob);

          // Create a canvas to convert SVG to PNG
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const img = new Image();

          img.onload = () => {
            // Set canvas size based on image
            canvas.width = img.width || 800;
            canvas.height = img.height || 600;

            // Fill with white background
            if (ctx) {
              ctx.fillStyle = 'white';
              ctx.fillRect(0, 0, canvas.width, canvas.height);

              // Draw the SVG image
              ctx.drawImage(img, 0, 0);

              // Convert to PNG data URL
              const imageData = canvas.toDataURL('image/png');

              // Use HTML img tag instead of markdown
              const imageHtml = `\n<img src="${imageData}" alt="Whiteboard Drawing" style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 8px; margin: 16px 0;" />\n`;

              // Append to content
              const newContent = editValue + imageHtml;
              setEditValue(newContent);

              // Clean up
              URL.revokeObjectURL(svgUrl);

              console.log('✅ Whiteboard image inserted!');
            }
          };

          img.onerror = () => {
            console.error('Failed to load SVG image');
            // Fallback: create a simple placeholder
            const canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 300;
            const ctx = canvas.getContext('2d');

            if (ctx) {
              ctx.fillStyle = 'white';
              ctx.fillRect(0, 0, 400, 300);
              ctx.strokeStyle = '#ddd';
              ctx.lineWidth = 2;
              ctx.strokeRect(0, 0, 400, 300);
              ctx.fillStyle = '#666';
              ctx.font = '16px Arial';
              ctx.textAlign = 'center';
              ctx.fillText('Whiteboard Drawing', 200, 150);

              const imageData = canvas.toDataURL('image/png');
              const imageHtml = `\n<img src="${imageData}" alt="Whiteboard Drawing" style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 8px; margin: 16px 0;" />\n`;

              const newContent = editValue + imageHtml;
              setEditValue(newContent);

              console.log('✅ Fallback whiteboard image inserted!');
            }
          };

          img.src = svgUrl;
        } else {
          console.log('No drawing content to save');
        }
      } else {
        console.log('No editor available');
      }
    } catch (error) {
      console.error('Error saving whiteboard:', error);
      toast.error('Failed to save whiteboard drawing');
    }

    setIsWhiteboardModalOpen(false);
  }, [editValue]);
  
  // Handle rich text deletion
  const handleDelete = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    onRemoveText(textItem.id);
  };
  
  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Ctrl+Enter to save
    if (e.ctrlKey && e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    }
    
    // Esc to cancel
    if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };
  
  // Custom components for ReactMarkdown
  const customComponents = {
    // Add custom components for markdown rendering if needed
  };
  
  // Render the card with conditional content based on edit mode
  return (
    <Card
      className={cn(
        "w-full h-full relative rich-text-card transition-all duration-300 ease-in-out",
        // Conditional borders based on borderless prop
        borderless
          ? "border-none shadow-none bg-transparent"
          : cn(
              // Subtle borders in view mode, more prominent when editing
              isEditMode
                ? (isEditing
                    ? "border-primary/40 ring-2 ring-primary/20 shadow-lg bg-card/80"
                    : "border-border/20 hover:border-border/40 bg-card/60 hover:bg-card/70")
                : "border-transparent hover:border-border/10 bg-card/40 hover:bg-card/60",
              // Smooth hover effects
              "hover:shadow-sm backdrop-blur-sm"
            )
      )}
      style={{
        zIndex: isEditing ? 100 : isHovered ? 10 : 'auto',
        cursor: isEditing ? 'default' : 'pointer'
      }}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      data-item-id={textItem.id}
      data-is-editing={isEditing ? 'true' : 'false'}
    >
      {/* Card Header with Controls - Hidden in cell context */}
      {isEditMode && !hideHeader && (
        <CardHeader
          className="flex flex-row items-center justify-between px-2 py-1 space-y-0 bg-primary/5 border-b card-header non-draggable"
          style={{
            opacity: 1, // Always visible in edit mode
            transition: 'opacity 0.2s ease',
            height: '20px',
            overflow: 'hidden'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex items-center gap-1">
            <Grip className="h-2.5 w-2.5 text-primary/70 draggable-handle" />
            <span className="text-xs font-medium text-primary/70">Markdown</span>
          </div>
          
          {!isEditing && (
            <div
              className="flex items-center gap-1 non-draggable"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
              }}
            >
              {onToggleFullscreen && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-5 w-5 p-0 hover:bg-green-100 hover:text-green-600 non-draggable"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    onToggleFullscreen();
                  }}
                  title="Fullscreen"
                >
                  <Maximize2 className="h-3 w-3" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 hover:bg-blue-100 hover:text-blue-600 non-draggable"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  handleEditClick(e);
                }}
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 hover:bg-red-100 hover:text-red-600 non-draggable"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  handleDelete(e);
                }}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          )}
        </CardHeader>
      )}
      
      {/* Card Content */}
      <CardContent
        className="p-0"
        style={{
          height: isEditMode ? 'calc(100% - 24px)' : '100%'
        }}
      >
        {isEditing ? (
          // Edit mode with markdown editor
          <div className="w-full h-full flex flex-col non-draggable">
            {/* Compact Toolbar - Hidden in cell context */}
            {!hideToolbar && (
              <div className="flex items-center justify-between border-b bg-muted/20 px-2 py-1">
                <MarkdownToolbar
                  onAction={handleAction}
                  setIsToolbarAction={setIsToolbarAction}
                  onWhiteboardOpen={() => setIsWhiteboardModalOpen(true)}
                />

                <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-5 text-xs px-2 non-draggable"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  className="h-5 text-xs px-2 non-draggable"
                  onClick={handleSave}
                >
                  Save
                </Button>
              </div>
            </div>
            )}

            {/* Compact Markdown Editor */}
            <textarea
              ref={textareaRef}
              value={editValue}
              onChange={handleChange}
              onBlur={handleBlur}
              onKeyDown={handleKeyDown}
              className="w-full resize-none p-2 bg-background border-none focus:outline-none focus:ring-0 font-mono text-xs min-h-[80px] non-draggable leading-relaxed"
              placeholder="Enter markdown content...
Supports: **bold**, *italic*, # headers, - lists, [links](url), ![images](url), `code`, tables, math $$x^2$$, and more!"
              style={{
                height: 'auto',
                fontSize: '12px',
                lineHeight: '1.5'
              }}
            />
            
            <div className="absolute bottom-3 right-3 bg-primary/10 text-primary text-xs px-2 py-1 rounded-sm">
              Ctrl+Enter to save, Esc to cancel
            </div>
          </div>
        ) : (
          // Enhanced view-only mode with GitHub-style markdown and responsive design
          <div
            className={cn(
              "w-full h-full overflow-auto max-w-none min-h-[40px]",
              // Responsive padding
              "p-2 sm:p-3 md:p-4",
              // Responsive text sizing
              "text-xs sm:text-sm",
              // Remove default prose styling to use custom components
              "prose-none",
              // Ensure proper overflow handling for long content
              "break-words overflow-wrap-anywhere",
              // Smooth scrolling
              "scroll-smooth",
              theme === 'dark' ? "dark" : ""
            )}
            style={{
              backgroundColor: 'transparent',
              textAlign: textItem.textAlign as any || 'left',
              lineHeight: '1.5',
              // Responsive font size
              fontSize: 'clamp(11px, 2vw, 14px)'
            }}
          >
            <ReactMarkdown
              remarkPlugins={[remarkGfm, remarkMath]}
              rehypePlugins={[rehypeRaw, rehypeHighlight, rehypeKatex]}
              components={customComponents}
              className="break-words overflow-hidden"
              skipHtml={false}
            >
              {editValue || `# Welcome to Markdown!

*Double-click to edit this text*

**Features supported:**
- **Bold** and *italic* text
- [Links](https://example.com) 🔗
- \`inline code\` and code blocks
- Tables, lists, and more!
- Math equations: $x^2 + y^2 = z^2$
- Images and diagrams

> Start typing to see the magic! ✨`}
            </ReactMarkdown>
          </div>
        )}
      </CardContent>
      
      {/* Custom resize handle - Only shown when hovered or in edit mode */}
      {isEditMode && !isEditing && (
        <div 
          className="absolute bottom-0 right-0 w-6 h-6 cursor-se-resize transition-opacity react-resizable-handle non-draggable"
          style={{
            zIndex: 5,
            opacity: isHovered ? 0.7 : 0,
            backgroundColor: 'rgba(var(--primary-rgb), 0.05)',
            borderTopLeftRadius: '4px',
            transition: 'opacity 0.2s ease, background-color 0.2s ease',
            boxShadow: '-1px -1px 2px rgba(0,0,0,0.05)'
          }}
        >
          <ChevronDown className="h-3 w-3 rotate-45 text-primary/70 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
        </div>
      )}

      {/* Whiteboard Modal Dialog */}
      <Dialog open={isWhiteboardModalOpen} onOpenChange={(open) => {
        if (!open) {
          handleWhiteboardClose(currentEditor);
        }
      }}>
        <DialogContent className="max-w-[98vw] max-h-[98vh] w-full h-full p-0 bg-white">
          <DialogHeader className="px-4 py-3 border-b bg-gray-50">
            <DialogTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Palette className="h-5 w-5 text-blue-600" />
                <span>Drawing Whiteboard</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleWhiteboardClose(currentEditor)}
              >
                Done
              </Button>
            </DialogTitle>
          </DialogHeader>

          <div
            className="flex-1 bg-white overflow-hidden"
            style={{
              height: 'calc(98vh - 80px)',
              width: '100%'
            }}
          >
            {typeof window !== 'undefined' && isWhiteboardModalOpen ? (
              <div
                className="w-full h-full"
                style={{
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                <Tldraw
                  persistenceKey={`markdown-whiteboard-${textItem.id || 'default'}`}
                  onMount={(editor) => {
                    console.log('✅ Tldraw mounted successfully!', editor);
                    setCurrentEditor(editor);
                  }}
                />
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-100">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <p className="text-gray-600">Initializing whiteboard...</p>
                </div>
              </div>
            )}
          </div>

          <div className="px-4 py-3 border-t bg-blue-50 dark:bg-blue-900/20">
            <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>
                <strong>Tip:</strong> Your drawing will be inserted at the cursor position in your text.
                Position your cursor where you want the image to appear before opening the whiteboard.
              </span>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
