'use client'

/**
 * DashboardToolbar Component
 *
 * This component provides the toolbar interface for the dashboard.
 * It includes controls for editing the dashboard, changing view modes,
 * adding text cards, and exporting the dashboard.
 *
 * Features:
 * - Toggle between edit and view modes
 * - Switch between grid and list views
 * - Add rich text editors to the dashboard
 * - Export dashboard as PNG or print
 * - Display dashboard statistics (number of items)
 */

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Edit, Save, LayoutGrid, LayoutList,
  Download, Printer, Share2, FileText
} from 'lucide-react';
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger, DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { HeadingItem, TextItem } from './types';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { toast } from 'sonner';

interface DashboardToolbarProps {
  isEditMode: boolean;
  viewMode: 'grid' | 'list';
  onToggleEditMode: () => void;
  onChangeView?: (view: 'grid' | 'list') => void;
  onSetViewMode?: (view: 'grid' | 'list') => void;
  onExport: () => void;
  onPrint: () => void;
  onAddRichText?: () => void;
  onShareDashboard?: () => void;
  chartsCount?: number;
  headingsCount?: number;
  textCount?: number;
  className?: string;
}

export function DashboardToolbar({
  isEditMode,
  viewMode,
  onToggleEditMode,
  onChangeView,
  onSetViewMode,
  onExport,
  onPrint,
  onAddRichText,
  onShareDashboard,
  chartsCount = 0,
  headingsCount = 0,
  textCount = 0,
  className
}: DashboardToolbarProps) {
  const [dashboardTitle, setDashboardTitle] = useState('My Dashboard');

  const handleViewModeChange = (mode: 'grid' | 'list') => {
    if (onChangeView) {
      onChangeView(mode);
    } else if (onSetViewMode) {
      onSetViewMode(mode);
    }
  };

  return (
    <div className={`border-b bg-background ${className || ''}`}>
      {/* Unified Toolbar */}
      <div className="flex items-center justify-between px-4 py-3">
        {/* Left Section - Title and View Mode */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <h2 className="text-lg font-semibold">{dashboardTitle}</h2>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => {
                const newTitle = prompt('Enter dashboard title:', dashboardTitle);
                if (newTitle) setDashboardTitle(newTitle);
              }}
            >
              <Edit className="h-3 w-3" />
            </Button>
          </div>

          {/* View Mode Selector */}
          <div className="flex items-center border rounded-md">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    className="h-8 w-8 p-0 rounded-r-none"
                    onClick={() => handleViewModeChange('grid')}
                  >
                    <LayoutGrid className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Grid View</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    className="h-8 w-8 p-0 rounded-l-none border-l"
                    onClick={() => handleViewModeChange('list')}
                  >
                    <LayoutList className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>List View</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          {/* Dashboard Stats */}
          <div className="text-sm text-muted-foreground">
            {chartsCount} chart{chartsCount !== 1 ? 's' : ''}
            {headingsCount > 0 && `, ${headingsCount} heading${headingsCount !== 1 ? 's' : ''}`}
            {textCount > 0 && `, ${textCount} text card${textCount !== 1 ? 's' : ''}`}
          </div>
        </div>

        {/* Right Section - Actions */}
        <div className="flex items-center gap-2">
          {/* Add Text Button - Only in edit mode */}
          {isEditMode && onAddRichText && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onAddRichText();
                toast.success('Adding text editor to dashboard...', {
                  duration: 2000
                });
              }}
            >
              <FileText className="h-4 w-4 mr-2" />
              Add Text
            </Button>
          )}

          {/* Edit/Save Button */}
          <Button
            variant={isEditMode ? 'default' : 'outline'}
            size="sm"
            onClick={onToggleEditMode}
          >
            {isEditMode ? (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save & Exit
              </>
            ) : (
              <>
                <Edit className="h-4 w-4 mr-2" />
                Edit Layout
              </>
            )}
          </Button>

          {/* Share & Export Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share & Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {onShareDashboard && (
                <>
                  <DropdownMenuItem onClick={onShareDashboard}>
                    <Share2 className="h-4 w-4 mr-2" />
                    Share Dashboard
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                </>
              )}
              <DropdownMenuItem onClick={onExport}>
                <Download className="h-4 w-4 mr-2" />
                Export as PNG
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onPrint}>
                <Printer className="h-4 w-4 mr-2" />
                Print Dashboard
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}