'use client'

import { memo } from 'react';
import { BaseDashboardCard } from './BaseDashboardCard';
import { useUnifiedContentRenderer } from './UnifiedContentRenderer';
import { DashboardItem, SavedChart, TableItem, PythonPlotItem, CalculatorResultItem } from './types';

interface UnifiedDashboardCardProps {
  item: DashboardItem;
  isEditMode: boolean;
  onUpdateItem: (itemId: string, updates: Partial<DashboardItem>) => void;
  onRemoveItem: (itemId: string) => void;
  onToggleFullscreen: (itemId: string) => void;
  onRefreshItem?: (itemId: string) => void;
  onEditItem?: (itemId: string) => void;
  className?: string;
  isDashboardChart?: boolean;
}

export const UnifiedDashboardCard = memo(function UnifiedDashboardCard({
  item,
  isEditMode,
  onUpdateItem,
  onRemoveItem,
  onToggleFullscreen,
  onRefreshItem,
  onEditItem,
  className,
  isDashboardChart = true
}: UnifiedDashboardCardProps) {

  // Get content renderer data
  const contentData = useUnifiedContentRenderer({
    item,
    isEditMode,
    isDashboardChart
  });

  // Determine if refresh is available for this item type
  const showRefresh = ['chart', 'table', 'pythonplot'].includes(item.type);
  
  // Determine if edit is available for this item type
  const showEdit = ['chart'].includes(item.type);

  // Handle refresh action
  const handleRefresh = (itemId: string) => {
    if (onRefreshItem) {
      onRefreshItem(itemId);
    }
  };

  // Handle edit action
  const handleEdit = (itemId: string) => {
    if (onEditItem) {
      onEditItem(itemId);
    }
  };

  // Get content-specific class names
  const getContentClassName = () => {
    switch (item.type) {
      case 'chart':
        return "dark:bg-black bg-white";
      case 'table':
        return "dark:bg-black bg-white";
      case 'pythonplot':
        return "dark:bg-black bg-white";
      case 'calculator':
        return "bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20";
      default:
        return "dark:bg-black bg-white";
    }
  };

  return (
    <BaseDashboardCard
      item={item}
      isEditMode={isEditMode}
      onUpdateItem={onUpdateItem}
      onRemoveItem={onRemoveItem}
      onToggleFullscreen={onToggleFullscreen}
      onRefreshItem={showRefresh ? handleRefresh : undefined}
      onEditItem={showEdit ? handleEdit : undefined}
      title={contentData.title}
      subtitle={contentData.subtitle}
      description={contentData.description}
      icon={contentData.icon}
      className={className}
      contentClassName={getContentClassName()}
      showRefresh={showRefresh}
      showEdit={showEdit && !!onEditItem}
    >
      {contentData.content}
    </BaseDashboardCard>
  );
});

export default UnifiedDashboardCard;
