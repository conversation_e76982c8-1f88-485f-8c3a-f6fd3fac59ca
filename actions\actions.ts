'use server';

import { revalidatePath } from "next/cache";
import { auth, currentUser } from "@clerk/nextjs/server";
import prisma from "@/lib/db";
import { Note } from "@prisma/client"; // Add this import



export async function getNote(id: string): Promise<Note | null> {
  try {
    const { userId: clerkUserId } = auth();

    if (!clerkUserId) {
      throw new Error("You must be logged in to fetch a note");
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!dbUser) {
      throw new Error("User not found in the database");
    }

    const note = await prisma.note.findUnique({
      where: { id: id, userId: dbUser.id },
    });

    // Handle content data type - keep as object for custom blocks
    return note;
  } catch (error) {
    console.error('Error in getNote:', error);
    throw error;
  }
}

export async function saveNote(
  id: string,
  title: string,
  content: string,
  coverImage: string | null,
  parentId?: string,
  mergedFromPublicId?: string,
  isMerged?: boolean,
  metadata?: string | null
): Promise<{ success: boolean; error?: string; noteId?: string }> {
  try {
    const { userId: clerkUserId } = auth();

    if (!clerkUserId) {
      return { success: false, error: "Authentication required" };
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      return { success: false, error: "User not found in database" };
    }

    // Parse content to JSON before saving
    let contentJson;
    try {
      contentJson = JSON.parse(content);

      // Process each block to ensure custom blocks are properly preserved
      contentJson = contentJson.map((block: any) => {
        if (block.type === 'dataAnalysis') {
          // For dataAnalysis blocks, preserve ALL properties exactly as they are
          return {
            ...block,
            props: {
              ...block.props,
              // BlockNote required props
              analysisType: block.props.analysisType || 'code',
              textAlignment: block.props.textAlignment || 'left',
              textColor: block.props.textColor || 'default',
              // Cell props
              savedQuery: block.props.savedQuery || '',
              language: block.props.language || 'sql',
              cellId: block.props.cellId || '',
              datasetId: block.props.datasetId || 'ds1',
              // Result data
              resultData: block.props.resultData || '[]',
              resultOutput: block.props.resultOutput || '',
              resultPlots: block.props.resultPlots || '[]',
              hasError: block.props.hasError || false,
              errorMessage: block.props.errorMessage || '',
              showGraphicWalker: block.props.showGraphicWalker || false,
              // Chart configuration
              chartType: block.props.chartType,
              chartConfig: block.props.chartConfig,
              // Add viewMode preservation
              viewMode: block.props.viewMode
            }
          };
        }
        return block;
      });
    } catch (error) {
      console.error('Error parsing content JSON:', error);
      return { success: false, error: "Failed to parse note content" };
    }

    // Determine metadata to save
    let metadataToSave = metadata;

    // If no explicit metadata but merging, create metadata
    if (!metadataToSave && isMerged) {
      const mergeMetadata = { mergedFromPublicId, mergedAt: new Date() };
      metadataToSave = JSON.stringify(mergeMetadata);
    }

    if (id) {
      // Update existing note
      await prisma.note.update({
        where: { id },
        data: {
          title,
          content: contentJson, // Store as JSON object
          coverImage,
          parentId,
          metadata: metadataToSave, // Use the processed metadata
          updatedAt: new Date(),
        },
      });
      return { success: true, noteId: id };
    } else {
      // Create new note with metadata
      const note = await prisma.note.create({
        data: {
          title,
          content: contentJson, // Store as JSON object
          coverImage,
          parentId,
          userId: dbUser.id,
          metadata: metadataToSave, // Use the processed metadata
        },
      });
      return { success: true, noteId: note.id };
    }
  } catch (error) {
    console.error('Error in saveNote:', error);
    return { success: false, error: "Failed to save note" };
  }
}

export async function getNotes() {
  try {
    const { userId: clerkUserId } = auth();

    if (!clerkUserId) {
      return { success: false, error: "Authentication required" };
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      return { success: false, error: "User not found in database" };
    }

    const notes = await prisma.note.findMany({
      where: {
        userId: dbUser.id,
      },
      orderBy: {
        updatedAt: 'desc',
      },
      include: {
        children: true,
      },
    });

    return {
      success: true,
      notes: notes.map(note => ({
        ...note,
        content: typeof note.content === 'object' ? JSON.stringify(note.content) : note.content
      }))
    };
  } catch (error) {
    console.error('Error in getNotes:', error);
    return { success: false, error: "Failed to fetch notes" };
  }
}

export async function createFolder(
  folderName: string,
  parentId?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId: clerkUserId } = auth();

    if (!clerkUserId) {
      return { success: false, error: "Authentication required" };
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      return { success: false, error: "User not found in database" };
    }

    await prisma.note.create({
      // @ts-ignore
      data: {
        title: folderName,
        content: JSON.stringify([]),
        isFolder: true,
        parentId,
        userId: dbUser.id,
      },
    });

    revalidatePath('/hr/workspace/note');
    return { success: true };
  } catch (error) {
    console.error('Error in createFolder:', error);
    return { success: false, error: "Failed to create folder" };
  }
}

export async function deleteNote(id: string): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId: clerkUserId } = auth();

    if (!clerkUserId) {
      return { success: false, error: "You must be logged in to delete a note" };
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!dbUser) {
      return { success: false, error: "User not found in the database" };
    }

    const note = await prisma.note.findUnique({
      where: { id: id, userId: dbUser.id },
      include: { children: true },
    });

    if (!note) {
      return { success: false, error: "Note not found" };
    }

    // If it's a folder, delete all children recursively
    if (note.isFolder) {
      await deleteNoteRecursive(id);
    } else {
      await prisma.note.delete({ where: { id: id } });
    }

    revalidatePath('/hr/workspace/note');
    return { success: true };
  } catch (error) {
    console.error('Error in deleteNote:', error);
    return { success: false, error: "An error occurred while deleting the note" };
  }
}

async function deleteNoteRecursive(noteId: string) {
  const note = await prisma.note.findUnique({
    where: { id: noteId },
    include: { children: true },
  });

  if (!note) return;

  for (const child of note.children) {
    await deleteNoteRecursive(child.id);
  }

  await prisma.note.delete({ where: { id: noteId } });
}

export async function publishNote(id: string) {
  try {
    const { userId: clerkUserId } = auth()
    if (!clerkUserId) {
      return { success: false, error: "Authentication required" }
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    })

    if (!dbUser) {
      return { success: false, error: "User not found" }
    }

    const note = await prisma.note.findUnique({
      where: { id, userId: dbUser.id },
    })

    if (!note) {
      return { success: false, error: "Note not found" }
    }

    // Generate a new publicId if not exists
    const newPublicId = note.publicId || `${id}-${crypto.randomUUID()}`

    // Check if publicId already exists (excluding current note)
    const existingNote = await prisma.note.findFirst({
      where: {
        publicId: newPublicId,
        NOT: {
          id: id
        }
      }
    })

    if (existingNote) {
      return { success: false, error: "Failed to generate unique public ID" }
    }

    await prisma.note.update({
      where: { id },
      data: {
        isPublished: true,
        publishedAt: new Date(),
        publicId: newPublicId,
      },
    })

    return { success: true, publicUrl: newPublicId }
  } catch (error) {
    console.error('Error publishing note:', error)
    return { success: false, error: "Failed to publish note" }
  }
}

export async function getPublicNote(publicId: string) {
  try {
    const note = await prisma.note.findFirst({
      where: {
        publicId,
        isPublished: true, // Only return published notes
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    // If note is found, ensure content is properly formatted
    if (note) {
      return {
        ...note,
        // Ensure content is parsed correctly for the client
        content: typeof note.content === "string"
          ? JSON.parse(note.content)
          : note.content,
        author: note.user,
      };
    }

    return null;
  } catch (error) {
    console.error('Error fetching public note:', error);
    return null;
  }
}



export async function getDetailedNotesData(): Promise<any[]> {
  try {
    const notes = await prisma.note.findMany({
      select: {
        id: true,
        title: true,
        content: true,
        coverImage: true,
        isFolder: true,
        isPublished: true,
        publishedAt: true,
        publicId: true,
        createdAt: true,
        updatedAt: true,

        parentId: true
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    return notes.map(note => ({
      ...note,
      createdAt: note.createdAt.toLocaleDateString(),
      updatedAt: note.updatedAt.toLocaleDateString(),
      publishedAt: note.publishedAt?.toLocaleDateString(),
      // Format content preview if it's a string
      contentPreview: typeof note.content === 'string'
        ? note.content.slice(0, 100) + (note.content.length > 100 ? '...' : '')
        : null
    }));
  } catch (error) {
    console.error('Failed to fetch detailed notes data:', error);
    return [];
  }
}

export async function getDetailedDiagramsData(): Promise<any[]> {
  try {
    const diagrams = await prisma.diagram.findMany({
      select: {
        id: true,
        title: true,


        content: true,
        createdAt: true,
        updatedAt: true,
        user: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    return diagrams.map(diagram => ({
      ...diagram,
      createdAt: diagram.createdAt.toLocaleDateString(),
      updatedAt: diagram.updatedAt.toLocaleDateString(),
      // Format content preview if it's a string
      contentPreview: typeof diagram.content === 'string'
        ? diagram.content.slice(0, 100) + (diagram.content.length > 100 ? '...' : '')
        : null
    }));
  } catch (error) {
    console.error('Failed to fetch detailed diagrams data:', error);
    return [];
  }
}


export async function updateNoteParent(noteId: string, newParentId: string | null) {
  try {
    // Ensure this matches your database structure
    const updated = await prisma.note.update({
      where: { id: noteId },
      data: {
        parentId: newParentId,
        updatedAt: new Date()
      },
    });

    return {
      success: true,
      note: updated
    };
  } catch (error) {
    console.error("Error updating note parent:", error);
    return {
      success: false,
      error: "Failed to update note parent"
    };
  }
}