'use client'

import { useRef } from 'react'
import { toast } from 'sonner'
import _ from 'lodash'
import { Dataset } from '@/types/index'

interface ServerStatus {
  status: 'healthy' | 'error' | 'warning'
  message: string
}

interface CellHandlersProps {
  id: string
  language: string
  onLanguageChange: (language: string) => void
  onSelectDatasets: (ids: string[]) => Promise<{ selectedList: Dataset[], datasetIds: string[] } | void>
  onRun: (id: string, code: string, shouldShowGraphicWalker?: boolean) => Promise<any>
  onContentChange?: (value: string) => void
  editorRef: any
  setIsLoadingDatasets: (loading: boolean) => void
  setIsDropdownOpen: (open: boolean) => void
  setIsRunning: (running: boolean) => void
  setExecutionTime: (time: { startTime?: Date; endTime?: Date }) => void
  setCurrentExecutionTime: (time: number) => void
  executionTimerRef: any
  setNeedsInput: (needs: boolean) => void
  setInputPrompt: (prompt: string) => void
  onViewModeChange?: (mode: 'table' | 'chart' | 'output' | 'graphicwalker') => void
}

export function useCellHandlers({
  id,
  language,
  onLanguageChange,
  onSelectDatasets,
  onRun,
  onContentChange,
  editorRef,
  setIsLoadingDatasets,
  setIsDropdownOpen,
  setIsRunning,
  setExecutionTime,
  setCurrentExecutionTime,
  executionTimerRef,
  setNeedsInput,
  setInputPrompt,
  onViewModeChange
}: CellHandlersProps) {

  // Dataset dropdown handlers
  const handleDropdownToggle = () => {
    setIsDropdownOpen(prev => !prev)
  }

  const handleDatasetSelection = async (datasetIds: string[]) => {
    setIsLoadingDatasets(true)
    try {
      // Call the parent callback - this should update the selectedDatasets prop
      const result = await onSelectDatasets(datasetIds)

      console.log('Datasets selected:', datasetIds)
      toast.success(`Selected ${datasetIds.length} dataset${datasetIds.length !== 1 ? 's' : ''}`)
    } catch (error) {
      console.error('Error selecting datasets:', error)
      toast.error('Failed to load datasets')
    } finally {
      setIsLoadingDatasets(false)
      setIsDropdownOpen(false)
    }
  }

  const handleRunCell = async () => {
    const code = editorRef.current?.getValue() || '';
    
    // Validation checks
    if (!code.trim()) {
      toast.error('Please enter some code to execute');
      return;
    }

    // Check for language mismatch
    if (language === 'python' && code.trim().startsWith('SELECT')) {
      toast.error('You are using SQL syntax with Python selected. Please change to SQL or update your code.');
      return;
    }

    if (language === 'sql' && (code.includes('import ') || code.includes('def ') || code.includes('print('))) {
      toast.error('You are using Python syntax with SQL selected. Please change to Python or update your code.');
      return;
    }

    setIsRunning(true);
    const startTime = new Date();
    setExecutionTime({ startTime });
    setCurrentExecutionTime(0);

    // Start live timer
    executionTimerRef.current = setInterval(() => {
      setCurrentExecutionTime(Date.now() - startTime.getTime());
    }, 100);

    try {
      // Check if this is a GraphicWalker command
      const shouldShowGraphicWalker = code.includes("--loopchart") || code.includes("-- loopchart");
      
      const result = await onRun(id, code, shouldShowGraphicWalker);
      
      const endTime = new Date();
      setExecutionTime({ startTime, endTime });

      // Stop the live timer
      if (executionTimerRef.current) {
        clearInterval(executionTimerRef.current);
        executionTimerRef.current = null;
      }

      // Handle input requests
      if (result?.needs_input) {
        setNeedsInput(true);
        setInputPrompt(result.input_prompt || 'Please provide input:');
        return; // Don't set isRunning to false yet
      }

      setIsRunning(false);

      // Auto-switch to appropriate view mode based on result
      if (shouldShowGraphicWalker && onViewModeChange) {
        onViewModeChange('graphicwalker');
      } else if (result?.data && result.data.length > 0 && onViewModeChange) {
        // Check if data has numeric columns for chart view
        const hasNumericData = Object.keys(result.data[0] || {}).some(
          key => typeof result.data[0][key] === 'number'
        );
        if (hasNumericData) {
          onViewModeChange('table'); // Start with table, user can switch to chart
        } else {
          onViewModeChange('table');
        }
      } else if (result?.plots && result.plots.length > 0 && onViewModeChange) {
        onViewModeChange('output'); // Show plots in output view
      }

    } catch (error) {
      console.error('Outer execution error:', error);
      const endTime = new Date();
      setExecutionTime({ startTime, endTime });

      // Stop the live timer
      if (executionTimerRef.current) {
        clearInterval(executionTimerRef.current);
        executionTimerRef.current = null;
      }

      setIsRunning(false);
      toast.error('Failed to execute code');
    }
  };

  const handleLanguageChange = (newLang: string) => {
    try {
      // When language changes, update the editor content with the default for that language
      if (editorRef.current && newLang !== language) {
        const currentContent = editorRef.current.getValue();
        // Only replace content if it's empty
        if (!currentContent.trim()) {
          editorRef.current.setValue('');
        }

        // Clear any previous results when changing languages
        // This prevents JSON parsing errors when switching languages
        if (onViewModeChange) {
          onViewModeChange('table'); // Reset to table view
        }
      }

      // Notify parent about language change
      onLanguageChange(newLang);

      // Show a toast to confirm language change
      toast.success(`Switched to ${newLang.charAt(0).toUpperCase() + newLang.slice(1)} language`);
    } catch (error) {
      console.error('Error changing language:', error);
      toast.error('Failed to change language');
    }
  };

  // Debounced content change handler
  const debouncedContentChange = useRef(
    _.debounce((value: string) => {
      if (onContentChange) {
        onContentChange(value);
      }
    }, 200)  // 200ms debounce
  ).current;

  return {
    handleDropdownToggle,
    handleDatasetSelection,
    handleRunCell,
    handleLanguageChange,
    debouncedContentChange
  }
}
