'use client'

import { useState, useEffect, use<PERSON>allback } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Slider } from '@/components/ui/slider';
import { 
  <PERSON>, 
  <PERSON>, 
  RotateCcw, 
  Eye, 
  EyeOff,
  Palette,
  Settings,
  BarChart3,
  LineChart,
  PieChart,
  AreaChart,
  Database,
  Filter,
  Layers,
  Zap,
  TrendingUp,
  Grid3X3,
  Type,
  Maximize2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { SavedChart } from './types';
import { EnhancedChartVisualizer } from '../ChartSectionsConf/EnhancedChartVisualizer';

interface ChartUpdateModalProps {
  isOpen: boolean;
  onClose: () => void;
  chart: SavedChart | null;
  onUpdateChart: (chartId: string, updates: Partial<SavedChart>) => void;
}

export function ChartUpdateModal({
  isOpen,
  onClose,
  chart,
  onUpdateChart
}: ChartUpdateModalProps) {
  const [editedChart, setEditedChart] = useState<SavedChart | null>(null);
  const [showPreview, setShowPreview] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize edited chart when modal opens
  useEffect(() => {
    if (isOpen && chart) {
      setEditedChart({ ...chart });
      setHasChanges(false);
    }
  }, [isOpen, chart]);

  // Handle escape key
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') handleClose();
    };
    
    if (isOpen) {
      window.addEventListener('keydown', handleEsc);
    }
    
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [isOpen]);

  const handleClose = useCallback(() => {
    if (hasChanges) {
      const confirmClose = window.confirm('You have unsaved changes. Are you sure you want to close?');
      if (!confirmClose) return;
    }
    onClose();
  }, [hasChanges, onClose]);

  const handleSave = useCallback(() => {
    if (!editedChart) return;
    
    onUpdateChart(editedChart.id, editedChart);
    setHasChanges(false);
    onClose();
  }, [editedChart, onUpdateChart, onClose]);

  const handleReset = useCallback(() => {
    if (!chart) return;
    setEditedChart({ ...chart });
    setHasChanges(false);
  }, [chart]);

  const updateChart = useCallback((updates: Partial<SavedChart>) => {
    if (!editedChart) return;
    
    setEditedChart(prev => prev ? { ...prev, ...updates } : null);
    setHasChanges(true);
  }, [editedChart]);

  const updateConfig = useCallback((configUpdates: Partial<SavedChart['config']>) => {
    if (!editedChart) return;
    
    setEditedChart(prev => prev ? {
      ...prev,
      config: { ...prev.config, ...configUpdates }
    } : null);
    setHasChanges(true);
  }, [editedChart]);

  if (!isOpen || !chart || !editedChart) return null;

  const chartTypeOptions = [
    { value: 'bar', label: 'Bar Chart', icon: BarChart3 },
    { value: 'line', label: 'Line Chart', icon: LineChart },
    { value: 'pie', label: 'Pie Chart', icon: PieChart },
    { value: 'area', label: 'Area Chart', icon: AreaChart }
  ];

  const aggregationOptions = [
    { value: 'none', label: 'None' },
    { value: 'sum', label: 'Sum' },
    { value: 'average', label: 'Average' },
    { value: 'min', label: 'Minimum' },
    { value: 'max', label: 'Maximum' },
    { value: 'count', label: 'Count' }
  ];

  const timeScaleOptions = [
    { value: 'none', label: 'None' },
    { value: 'day', label: 'Daily' },
    { value: 'week', label: 'Weekly' },
    { value: 'month', label: 'Monthly' },
    { value: 'year', label: 'Yearly' }
  ];

  // Get available columns from data
  const availableColumns = editedChart.data && editedChart.data.length > 0 
    ? Object.keys(editedChart.data[0]) 
    : [];

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-0 gap-0">
        {/* Header */}
        <DialogHeader className="flex flex-row items-center justify-between p-4 border-b">
          <div className="flex items-center gap-3">
            <BarChart3 className="h-5 w-5" />
            <DialogTitle className="text-lg font-semibold">
              Edit Chart: {editedChart.title || editedChart.config.title || 'Untitled Chart'}
            </DialogTitle>
            {hasChanges && (
              <Badge variant="outline" className="text-orange-600 border-orange-600">
                Unsaved Changes
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPreview(!showPreview)}
              className="gap-2"
            >
              {showPreview ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showPreview ? 'Hide Preview' : 'Show Preview'}
            </Button>
            
            <Separator orientation="vertical" className="h-6" />
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={!hasChanges}
              className="gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset
            </Button>
            
            <Button
              onClick={handleSave}
              disabled={!hasChanges}
              className="gap-2"
            >
              <Save className="h-4 w-4" />
              Save Changes
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Settings Panel - Enhanced like Superset */}
          <div className="w-96 border-r bg-muted/30 flex flex-col">
            {/* Panel Header */}
            <div className="p-4 border-b bg-background/50">
              <div className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                <h3 className="font-medium">Chart Configuration</h3>
              </div>
            </div>

            {/* Scrollable Content */}
            <ScrollArea className="flex-1">
              <div className="p-4 space-y-6">
                <Tabs defaultValue="data" className="w-full">
                  <TabsList className="grid w-full grid-cols-4 text-xs">
                    <TabsTrigger value="data" className="gap-1">
                      <Database className="h-3 w-3" />
                      Data
                    </TabsTrigger>
                    <TabsTrigger value="customize" className="gap-1">
                      <Palette className="h-3 w-3" />
                      Style
                    </TabsTrigger>
                    <TabsTrigger value="filters" className="gap-1">
                      <Filter className="h-3 w-3" />
                      Filters
                    </TabsTrigger>
                    <TabsTrigger value="advanced" className="gap-1">
                      <Zap className="h-3 w-3" />
                      Advanced
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="data" className="space-y-4 mt-4">
                    {/* Chart Metadata */}
                    <Card>
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          <Type className="h-4 w-4" />
                          <CardTitle className="text-sm">Chart Information</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="space-y-2">
                          <Label htmlFor="title" className="text-xs font-medium">Chart Title</Label>
                          <Input
                            id="title"
                            value={editedChart.config.title || ''}
                            onChange={(e) => updateConfig({ title: e.target.value })}
                            placeholder="Enter chart title"
                            className="h-8"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="description" className="text-xs font-medium">Description</Label>
                          <Textarea
                            id="description"
                            value={editedChart.config.description || ''}
                            onChange={(e) => updateConfig({ description: e.target.value })}
                            placeholder="Enter chart description"
                            rows={2}
                            className="text-xs"
                          />
                        </div>
                      </CardContent>
                    </Card>

                    {/* Chart Type Selection */}
                    <Card>
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          <BarChart3 className="h-4 w-4" />
                          <CardTitle className="text-sm">Visualization Type</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-2">
                          {chartTypeOptions.map((option) => {
                            const Icon = option.icon;
                            const isSelected = editedChart.config.type === option.value;
                            return (
                              <Button
                                key={option.value}
                                variant={isSelected ? "default" : "outline"}
                                size="sm"
                                onClick={() => updateConfig({ type: option.value as any })}
                                className={cn(
                                  "h-16 flex-col gap-1 text-xs",
                                  isSelected && "bg-primary text-primary-foreground"
                                )}
                              >
                                <Icon className="h-5 w-5" />
                                {option.label}
                              </Button>
                            );
                          })}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Data Mapping */}
                    <Card>
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          <Grid3X3 className="h-4 w-4" />
                          <CardTitle className="text-sm">Data Mapping</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="space-y-2">
                          <Label className="text-xs font-medium">X Axis (Dimensions)</Label>
                          <Select
                            value={editedChart.config.xAxis}
                            onValueChange={(value) => updateConfig({ xAxis: value })}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue placeholder="Select X axis column" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableColumns.map((column) => (
                                <SelectItem key={column} value={column}>
                                  <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                                    {column}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-xs font-medium">Y Axis (Metrics)</Label>
                          <Select
                            value={editedChart.config.yAxis}
                            onValueChange={(value) => updateConfig({ yAxis: value })}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue placeholder="Select Y axis column" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableColumns.map((column) => (
                                <SelectItem key={column} value={column}>
                                  <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                                    {column}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="customize" className="space-y-4 mt-4">
                    {/* Color Scheme */}
                    <Card>
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          <Palette className="h-4 w-4" />
                          <CardTitle className="text-sm">Color & Theme</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="space-y-2">
                          <Label className="text-xs font-medium">Primary Color</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              type="color"
                              value={editedChart.config.color || '#8884d8'}
                              onChange={(e) => updateConfig({ color: e.target.value })}
                              className="w-12 h-8 p-1 border rounded"
                            />
                            <Input
                              value={editedChart.config.color || '#8884d8'}
                              onChange={(e) => updateConfig({ color: e.target.value })}
                              placeholder="#8884d8"
                              className="h-8 text-xs"
                            />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Display Options */}
                    <Card>
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          <Eye className="h-4 w-4" />
                          <CardTitle className="text-sm">Display Options</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label className="text-xs font-medium">Show Legend</Label>
                          <Switch
                            checked={editedChart.config.showLegend}
                            onCheckedChange={(checked) => updateConfig({ showLegend: checked })}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label className="text-xs font-medium">Show Data Labels</Label>
                          <Switch
                            checked={editedChart.config.showLabels}
                            onCheckedChange={(checked) => updateConfig({ showLabels: checked })}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label className="text-xs font-medium">Show Grid Lines</Label>
                          <Switch
                            checked={editedChart.config.showGrid}
                            onCheckedChange={(checked) => updateConfig({ showGrid: checked })}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="filters" className="space-y-4 mt-4">
                    <Card>
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          <Filter className="h-4 w-4" />
                          <CardTitle className="text-sm">Data Filters</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center py-8 text-muted-foreground">
                          <Filter className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">Advanced filtering coming soon</p>
                          <p className="text-xs">Filter data by conditions and ranges</p>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="advanced" className="space-y-4 mt-4">
                    {/* Data Processing */}
                    <Card>
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          <TrendingUp className="h-4 w-4" />
                          <CardTitle className="text-sm">Data Processing</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="space-y-2">
                          <Label className="text-xs font-medium">Aggregation Method</Label>
                          <Select
                            value={editedChart.config.aggregation || 'none'}
                            onValueChange={(value) => updateConfig({ aggregation: value as any })}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {aggregationOptions.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-xs font-medium">Time Scale</Label>
                          <Select
                            value={editedChart.config.timeScale || 'none'}
                            onValueChange={(value) => updateConfig({ timeScale: value as any })}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {timeScaleOptions.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Interactive Features */}
                    <Card>
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          <Maximize2 className="h-4 w-4" />
                          <CardTitle className="text-sm">Interactive Features</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label className="text-xs font-medium">Enable Zoom & Pan</Label>
                          <Switch
                            checked={editedChart.config.enableZoom || false}
                            onCheckedChange={(checked) => updateConfig({ enableZoom: checked })}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label className="text-xs font-medium">Multi-Series Chart</Label>
                          <Switch
                            checked={editedChart.config.multiSeries || false}
                            onCheckedChange={(checked) => updateConfig({ multiSeries: checked })}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </div>
            </ScrollArea>
          </div>

          {/* Enhanced Preview Panel */}
          {showPreview && (
            <div className="flex-1 flex flex-col bg-background">
              {/* Preview Header */}
              <div className="p-4 border-b bg-muted/30">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    <h3 className="font-medium">Live Preview</h3>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {editedChart.config.type?.toUpperCase()} CHART
                  </Badge>
                </div>
              </div>

              {/* Preview Content */}
              <div className="flex-1 p-4 overflow-hidden">
                <div className="w-full h-full bg-card rounded-lg border p-4">
                  <EnhancedChartVisualizer
                    data={editedChart.data}
                    initialChartType={editedChart.config.type}
                    chartConfig={editedChart.config}
                    showConfig={false}
                    fullHeight={true}
                    isDashboardChart={false}
                    className="w-full h-full"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
