'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Database, ChevronDown, Code2, Play, Loader2, Maximize2, MoveUp, MoveDown, FileText, Bold, Trash2 } from "lucide-react"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from 'sonner'
import { cn } from "@/lib/utils"
import { Dataset } from '@/types/index'
import { AIAssistant } from '../AIAssistant'
import { KeyboardShortcutsHelp } from '../KeyboardShortcutsHelp'

interface ServerStatus {
  status: 'healthy' | 'error' | 'warning'
  message: string
}

interface CellToolbarProps {
  cellType: 'code' | 'markdown'
  isHovered: boolean
  language: string
  onLanguageChange: (language: string) => void
  selectedDatasets: Dataset[]
  availableDatasets: Dataset[]
  isDropdownOpen: boolean
  handleDropdownToggle: () => void
  handleDatasetSelection: (datasetIds: string[]) => void
  serverStatus: ServerStatus
  isRunning: boolean
  currentExecutionTime: number
  handleRunCell: () => void
  onMoveUp?: (id: string) => void
  onMoveDown?: (id: string) => void
  onConvertCellType?: (id: string, targetType: 'code' | 'markdown') => void
  isEditingMarkdown: boolean
  setIsEditingMarkdown: (value: boolean) => void
  setIsFullscreenOpen: (value: boolean) => void
  onDelete: (id: string) => void
  onContentChange?: (value: string) => void
  editorRef: any
  id: string
  markdownCellRef: any
}

export function CellToolbar({
  cellType,
  isHovered,
  language,
  onLanguageChange,
  selectedDatasets,
  availableDatasets,
  isDropdownOpen,
  handleDropdownToggle,
  handleDatasetSelection,
  serverStatus,
  isRunning,
  currentExecutionTime,
  handleRunCell,
  onMoveUp,
  onMoveDown,
  onConvertCellType,
  isEditingMarkdown,
  setIsEditingMarkdown,
  setIsFullscreenOpen,
  onDelete,
  onContentChange,
  editorRef,
  id,
  markdownCellRef
}: CellToolbarProps) {
  return (
    <div className={cn(
      "absolute right-1 top-1 z-30 flex items-center gap-0.5",
      cellType === 'markdown'
        ? "bg-background/30 backdrop-blur-sm rounded-md px-0.5 py-0.5"
        : "bg-background/50 backdrop-blur-sm rounded-md px-0.5 py-0.5",
      "transition-opacity duration-200",
      isHovered ? "opacity-100" : "opacity-0 pointer-events-none"
    )}>

      {/* Dataset Selector - Compact version */}
      {cellType !== 'markdown' && (
        <div className="relative" data-dropdown-id={id}>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-1.5 hover:bg-accent flex items-center gap-1"
            onClick={handleDropdownToggle}
          >
            <Database className="h-3 w-3" />
            <span className="text-xs truncate max-w-[80px]">
              {selectedDatasets.length === 1
                ? selectedDatasets[0].name
                : selectedDatasets.length > 1
                  ? `${selectedDatasets.length} datasets`
                  : 'Select Datasets'}
            </span>
            <ChevronDown className="h-3 w-3" />
          </Button>

          {isDropdownOpen && (
            <div
              className="absolute top-full right-0 mt-1 w-[200px] bg-background rounded-md border shadow-lg z-[100]"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="max-h-[200px] overflow-y-auto">
                {availableDatasets.length === 0 ? (
                  <div className="px-2 py-1.5 text-xs text-muted-foreground">
                    {serverStatus.status === 'healthy'
                      ? 'No datasets found in database. Upload some datasets to get started.'
                      : 'No datasets available'
                    }
                  </div>
                ) : (
                  <div className="py-1">
                    {availableDatasets.map(dataset => (
                      <div
                        key={dataset.id}
                        className="flex items-center px-2 py-1 hover:bg-accent hover:text-accent-foreground cursor-pointer"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const isCurrentlySelected = selectedDatasets.some(d => d.id === dataset.id);
                          const currentIds = selectedDatasets.map(d => d.id);
                          const newIds = isCurrentlySelected
                            ? currentIds.filter(id => id !== dataset.id)
                            : [...currentIds, dataset.id];
                          console.log('Row clicked:', dataset.name, 'New selection:', newIds);
                          handleDatasetSelection(newIds);
                        }}
                      >
                        <Checkbox
                          id={`dataset-${dataset.id}`}
                          checked={selectedDatasets.some(d => d.id === dataset.id)}
                          onCheckedChange={(checked) => {
                            console.log('Checkbox clicked:', dataset.name, checked);
                            const currentIds = selectedDatasets.map(d => d.id);
                            const newIds = checked
                              ? [...currentIds, dataset.id]
                              : currentIds.filter(id => id !== dataset.id);
                            console.log('New dataset IDs:', newIds);
                            handleDatasetSelection(newIds);
                          }}
                          className="mr-2 h-3 w-3 pointer-events-auto"
                          onClick={(e) => e.stopPropagation()}
                        />
                        <label
                          htmlFor={`dataset-${dataset.id}`}
                          className="text-xs flex-1 cursor-pointer select-none"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            const isCurrentlySelected = selectedDatasets.some(d => d.id === dataset.id);
                            const currentIds = selectedDatasets.map(d => d.id);
                            const newIds = isCurrentlySelected
                              ? currentIds.filter(id => id !== dataset.id)
                              : [...currentIds, dataset.id];
                            handleDatasetSelection(newIds);
                          }}
                        >
                          {dataset.name}
                        </label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Language Selector - Only for code cells */}
      {cellType !== 'markdown' && (
        <Select
          value={language}
          onValueChange={(newLang) => {
            try {
              onLanguageChange(newLang);
              toast.success(`Switched to ${newLang.charAt(0).toUpperCase() + newLang.slice(1)} language`);
            } catch (error) {
              console.error('Error changing language:', error);
              toast.error('Failed to change language');
            }
          }}
        >
          <SelectTrigger className="h-6 w-[90px] text-xs px-1.5">
            <Code2 className="h-3 w-3 mr-1" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="sql" className="text-xs">SQL</SelectItem>
            <SelectItem value="python" className="text-xs">Python</SelectItem>
            <SelectItem value="javascript" className="text-xs">JavaScript</SelectItem>
          </SelectContent>
        </Select>
      )}

      {/* Run Button - Only for code cells */}
      {cellType !== 'markdown' && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRunCell}
                disabled={isRunning}
                className={cn(
                  "h-5 w-5 p-0",
                  "hover:bg-accent active:scale-95",
                  "transition-transform duration-100"
                )}
              >
                {isRunning ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <Play className="h-3 w-3" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">
                {isRunning
                  ? `Running... ${(currentExecutionTime / 1000).toFixed(2)}s`
                  : 'Run cell (Ctrl+Enter)'
                }
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* Move Up Button */}
      {onMoveUp && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                onClick={() => onMoveUp(id)}
              >
                <MoveUp className="h-3 w-3 text-muted-foreground" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">Move cell up (Ctrl+Shift+↑)</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* Move Down Button */}
      {onMoveDown && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                onClick={() => onMoveDown(id)}
              >
                <MoveDown className="h-3 w-3 text-muted-foreground" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">Move cell down (Ctrl+Shift+↓)</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* Fullscreen Editor Button for Markdown Cells */}
      {cellType === 'markdown' && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                onClick={() => {
                  markdownCellRef.current?.openFullscreen()
                }}
              >
                <Maximize2 className="h-3 w-3 text-muted-foreground" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">Fullscreen markdown editor</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* Fullscreen Editor Button for Code Cells */}
      {cellType !== 'markdown' && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFullscreenOpen(true)}
                className="h-6 w-6 p-0 hover:bg-accent rounded-full"
              >
                <Maximize2 className="h-3 w-3 text-muted-foreground" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">Fullscreen editor</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* Convert Cell Type Button */}
      {onConvertCellType && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                onClick={() =>
                  onConvertCellType(id, cellType === 'code' ? 'markdown' : 'code')
                }
              >
                {cellType === 'code'
                  ? <FileText className="h-3 w-3 text-muted-foreground" />
                  : <Code2 className="h-3 w-3 text-muted-foreground" />
                }
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">
                Convert to {cellType === 'code' ? 'markdown' : 'code'} cell
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* Edit Markdown button for markdown cells */}
      {cellType === 'markdown' && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                onClick={() => setIsEditingMarkdown(!isEditingMarkdown)}
              >
                <Bold className="h-3 w-3 text-muted-foreground" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">
                {isEditingMarkdown ? "Exit Edit Mode" : "Edit Markdown"}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* AI Assistant Button - only for code cells */}
      {cellType === 'code' && onContentChange && (
        <div className="ml-2">
          <AIAssistant
            selectedDatasets={selectedDatasets}
            language={language as 'sql' | 'python' | 'javascript' | 'markdown'}
            onCodeGenerated={(code) => {
              onContentChange(code);
              toast.success('AI-generated code inserted!');
            }}
            editorRef={editorRef}
          />
        </div>
      )}

      {/* Keyboard Shortcuts Help */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div>
              <KeyboardShortcutsHelp />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-xs">Keyboard shortcuts</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* Delete Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onDelete(id)}
        className="h-6 w-6 p-0 hover:bg-destructive hover:text-destructive-foreground"
      >
        <Trash2 className="h-3 w-3" />
      </Button>
    </div>
  )
}
