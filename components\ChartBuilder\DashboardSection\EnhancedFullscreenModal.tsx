'use client'

import { useState, useEffect, useRef, useCallback } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  X,
  Maximize2,
  Minimize2,
  Download,
  Share2,
  Edit3,
  RefreshCw,
  Settings,
  Filter,
  MoreHorizontal,
  Expand,
  Shrink,
  Bold,
  Italic,
  List,
  ListOrdered,
  Code,
  Link,
  Image as ImageIcon,
  Table as TableIcon,
  Quote,
  Hash,
  Minus,
  Save,
  Eye
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { DashboardItem, TextItem } from './types';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import useUnifiedContentRenderer from './UnifiedContentRenderer';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { toast } from 'sonner';

interface EnhancedFullscreenModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: DashboardItem | null;
  onUpdateItem?: (itemId: string, updates: Partial<DashboardItem>) => void;
  onRefreshItem?: (itemId: string) => void;
  onEditItem?: (itemId: string) => void;
  onExportItem?: (itemId: string, format: 'png' | 'pdf' | 'csv' | 'json') => void;
  onShareItem?: (itemId: string) => void;
}

export function EnhancedFullscreenModal({
  isOpen,
  onClose,
  item,
  onUpdateItem,
  onRefreshItem,
  onEditItem,
  onExportItem,
  onShareItem
}: EnhancedFullscreenModalProps) {
  const [isMaximized, setIsMaximized] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Text editing state
  const [isEditingText, setIsEditingText] = useState(false);
  const [editValue, setEditValue] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Handle escape key
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };
    
    if (isOpen) {
      window.addEventListener('keydown', handleEsc);
      document.body.style.overflow = 'hidden';
    }
    
    return () => {
      window.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, onClose]);

  // Initialize edit value when item changes
  useEffect(() => {
    if (item && item.type === 'text') {
      const textItem = item as TextItem;
      setEditValue(textItem.content || '');
      setIsEditingText(false); // Start in view mode
    }
  }, [item]);

  // Handle refresh with loading state
  const handleRefresh = useCallback(async () => {
    if (!item || !onRefreshItem) return;
    
    setIsLoading(true);
    try {
      await onRefreshItem(item.id);
    } finally {
      setIsLoading(false);
    }
  }, [item, onRefreshItem]);

  // Handle export
  const handleExport = useCallback((format: 'png' | 'pdf' | 'csv' | 'json') => {
    if (!item || !onExportItem) return;
    onExportItem(item.id, format);
  }, [item, onExportItem]);

  // Handle edit
  const handleEdit = useCallback(() => {
    if (!item || !onEditItem) return;
    onEditItem(item.id);
  }, [item, onEditItem]);

  // Handle share
  const handleShare = useCallback(() => {
    if (!item || !onShareItem) return;
    onShareItem(item.id);
  }, [item, onShareItem]);

  // Text editing functions
  const handleTextEdit = () => {
    if (item?.type === 'text') {
      setIsEditingText(true);
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.focus();
        }
      }, 100);
    }
  };

  const handleTextSave = () => {
    if (item?.type === 'text' && onUpdateItem) {
      onUpdateItem(item.id, { content: editValue });
      setIsEditingText(false);
      toast.success('Text saved successfully');
    }
  };

  const handleTextCancel = () => {
    if (item?.type === 'text') {
      const textItem = item as TextItem;
      setEditValue(textItem.content || '');
      setIsEditingText(false);
    }
  };

  // Markdown toolbar action handler
  const handleToolbarAction = (action: string) => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = editValue.substring(start, end);

    let newText = editValue;
    let newCursorPos = end;

    switch (action) {
      case 'bold':
        newText = editValue.substring(0, start) + `**${selectedText}**` + editValue.substring(end);
        newCursorPos = end + 4;
        break;
      case 'italic':
        newText = editValue.substring(0, start) + `*${selectedText}*` + editValue.substring(end);
        newCursorPos = end + 2;
        break;
      case 'bulletList':
        newText = editValue.substring(0, start) + `- ${selectedText || 'List item'}` + editValue.substring(end);
        newCursorPos = start + (selectedText ? selectedText.length + 2 : 11);
        break;
      case 'numberedList':
        newText = editValue.substring(0, start) + `1. ${selectedText || 'List item'}` + editValue.substring(end);
        newCursorPos = start + (selectedText ? selectedText.length + 3 : 12);
        break;
      case 'code':
        if (selectedText.includes('\n')) {
          newText = editValue.substring(0, start) + '```\n' + selectedText + '\n```' + editValue.substring(end);
          newCursorPos = end + 8;
        } else {
          newText = editValue.substring(0, start) + '`' + selectedText + '`' + editValue.substring(end);
          newCursorPos = end + 2;
        }
        break;
      case 'link':
        newText = editValue.substring(0, start) + `[${selectedText || 'Link text'}](url)` + editValue.substring(end);
        newCursorPos = start + (selectedText ? selectedText.length + 3 : 11);
        break;
      case 'image':
        newText = editValue.substring(0, start) + `![${selectedText || 'Alt text'}](url)` + editValue.substring(end);
        newCursorPos = start + (selectedText ? selectedText.length + 4 : 12);
        break;
      case 'table':
        newText = editValue.substring(0, start) +
          '| Header 1 | Header 2 | Header 3 |\n' +
          '| -------- | -------- | -------- |\n' +
          '| Cell 1   | Cell 2   | Cell 3   |\n' +
          '| Cell 4   | Cell 5   | Cell 6   |' +
          editValue.substring(end);
        newCursorPos = start + 129;
        break;
      case 'quote':
        newText = editValue.substring(0, start) + `> ${selectedText || 'Quote text'}` + editValue.substring(end);
        newCursorPos = start + (selectedText ? selectedText.length + 2 : 12);
        break;
      case 'heading':
        newText = editValue.substring(0, start) + `# ${selectedText || 'Heading'}` + editValue.substring(end);
        newCursorPos = start + (selectedText ? selectedText.length + 2 : 9);
        break;
      case 'divider':
        newText = editValue.substring(0, start) + '\n---\n' + editValue.substring(end);
        newCursorPos = start + 5;
        break;
    }

    setEditValue(newText);

    // Set cursor position after state update
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
      }
    }, 0);
  };

  if (!isOpen || !item) return null;

  // Get content renderer data
  const contentData = useUnifiedContentRenderer({
    item,
    isEditMode: false,
    isDashboardChart: false
  });

  // Get available export formats based on item type
  const getExportFormats = () => {
    switch (item.type) {
      case 'chart':
        return ['png', 'pdf', 'csv', 'json'];
      case 'table':
        return ['csv', 'json', 'pdf'];
      case 'pythonplot':
        return ['png', 'pdf'];
      case 'calculator':
        return ['json', 'pdf'];
      default:
        return ['json'];
    }
  };

  const exportFormats = getExportFormats();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent 
        className={cn(
          "p-0 gap-0 transition-all duration-300 ease-in-out",
          isMaximized 
            ? "max-w-[98vw] max-h-[98vh] w-[98vw] h-[98vh]" 
            : "max-w-[90vw] max-h-[90vh] w-[90vw] h-[90vh]"
        )}
        ref={modalRef}
      >
        {/* Header */}
        <DialogHeader className="flex flex-row items-center justify-between p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              {contentData.icon}
              <DialogTitle className="text-lg font-semibold">
                {contentData.title}
              </DialogTitle>
            </div>
            <Badge variant="secondary" className="text-xs">
              {contentData.subtitle}
            </Badge>
            {isLoading && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <RefreshCw className="h-3 w-3 animate-spin" />
                <span>Refreshing...</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-1">
            {/* Refresh Button */}
            {onRefreshItem && ['chart', 'table', 'pythonplot'].includes(item.type) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
                className="h-8 w-8 p-0"
              >
                <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
              </Button>
            )}

            {/* Edit Button */}
            {onEditItem && item.type === 'chart' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEdit}
                className="h-8 w-8 p-0"
              >
                <Edit3 className="h-4 w-4" />
              </Button>
            )}

            {/* Text Edit/Preview Toggle Button */}
            {item.type === 'text' && (
              <>
                {!isEditingText ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleTextEdit}
                    className="h-8 w-8 p-0"
                    title="Edit Text"
                  >
                    <Edit3 className="h-4 w-4" />
                  </Button>
                ) : (
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowPreview(!showPreview)}
                      className="h-8 w-8 p-0"
                      title={showPreview ? "Show Editor" : "Show Preview"}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleTextCancel}
                      className="h-8 px-2"
                      title="Cancel"
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={handleTextSave}
                      className="h-8 px-2"
                      title="Save"
                    >
                      <Save className="h-4 w-4 mr-1" />
                      Save
                    </Button>
                  </div>
                )}
              </>
            )}

            {/* Export Dropdown */}
            {onExportItem && exportFormats.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Download className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {exportFormats.map((format) => (
                    <DropdownMenuItem 
                      key={format} 
                      onClick={() => handleExport(format as any)}
                    >
                      Export as {format.toUpperCase()}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* Share Button */}
            {onShareItem && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleShare}
                className="h-8 w-8 p-0"
              >
                <Share2 className="h-4 w-4" />
              </Button>
            )}

            <Separator orientation="vertical" className="h-6" />

            {/* Settings Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
              className="h-8 w-8 p-0"
            >
              <Settings className="h-4 w-4" />
            </Button>

            {/* Maximize/Minimize Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMaximized(!isMaximized)}
              className="h-8 w-8 p-0"
            >
              {isMaximized ? <Shrink className="h-4 w-4" /> : <Expand className="h-4 w-4" />}
            </Button>

            {/* Close Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        {/* Content Area */}
        <div className="flex-1 relative overflow-hidden">
          {item.type === 'text' && isEditingText ? (
            /* Text Editing Mode */
            <div className="w-full h-full flex flex-col">
              {/* Markdown Toolbar */}
              <div className="flex items-center gap-1 p-2 border-b bg-muted/20 flex-wrap">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleToolbarAction('bold')}
                  title="Bold"
                >
                  <Bold className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleToolbarAction('italic')}
                  title="Italic"
                >
                  <Italic className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleToolbarAction('bulletList')}
                  title="Bullet List"
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleToolbarAction('numberedList')}
                  title="Numbered List"
                >
                  <ListOrdered className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleToolbarAction('code')}
                  title="Code"
                >
                  <Code className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleToolbarAction('link')}
                  title="Link"
                >
                  <Link className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleToolbarAction('image')}
                  title="Image"
                >
                  <ImageIcon className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleToolbarAction('table')}
                  title="Table"
                >
                  <TableIcon className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleToolbarAction('quote')}
                  title="Quote"
                >
                  <Quote className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleToolbarAction('heading')}
                  title="Heading"
                >
                  <Hash className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleToolbarAction('divider')}
                  title="Divider"
                >
                  <Minus className="h-4 w-4" />
                </Button>
              </div>

              {/* Editor/Preview Area */}
              <div className="flex-1 flex">
                {/* Editor */}
                <div className={cn("flex-1 flex flex-col", showPreview && "w-1/2")}>
                  <textarea
                    ref={textareaRef}
                    value={editValue}
                    onChange={(e) => setEditValue(e.target.value)}
                    className="flex-1 w-full p-4 bg-background border-none focus:outline-none focus:ring-0 font-mono text-sm resize-none"
                    placeholder="Enter markdown content..."
                    style={{ minHeight: '400px' }}
                  />
                </div>

                {/* Preview */}
                {showPreview && (
                  <>
                    <Separator orientation="vertical" />
                    <div className="flex-1 w-1/2 overflow-auto p-4">
                      <div className="prose prose-sm max-w-none dark:prose-invert">
                        <ReactMarkdown
                          remarkPlugins={[remarkGfm]}
                          rehypePlugins={[rehypeRaw]}
                          className="break-words"
                        >
                          {editValue || '*No content*'}
                        </ReactMarkdown>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          ) : (
            /* Normal Content Display */
            <div
              ref={contentRef}
              className={cn(
                "w-full h-full",
                item.type === 'chart' || item.type === 'pythonplot'
                  ? "p-2" // Minimal padding for charts and plots to maximize space
                  : "p-6" // Normal padding for other content
              )}
              style={{ height: 'calc(100% - 0px)' }}
            >
              {/* Enhanced content rendering with fullscreen optimizations */}
              <div className={cn(
                "w-full h-full",
                item.type === 'chart' && "bg-white dark:bg-gray-900 rounded-lg border",
                item.type === 'pythonplot' && "bg-white dark:bg-gray-900 rounded-lg border"
              )}>
                {contentData.content}
              </div>
            </div>
          )}
        </div>

        {/* Footer with description */}
        {contentData.description && (
          <div className="border-t bg-muted/30 px-4 py-2">
            <p className="text-sm text-muted-foreground">
              {contentData.description}
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
