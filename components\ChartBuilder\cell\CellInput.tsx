'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Send } from "lucide-react"
import { toast } from 'sonner'

interface CellInputProps {
  needsInput: boolean
  inputPrompt: string
  onSubmitInput: (input: string) => Promise<void>
  setNeedsInput: (needs: boolean) => void
  setIsRunning: (running: boolean) => void
}

export function CellInput({
  needsInput,
  inputPrompt,
  onSubmitInput,
  setNeedsInput,
  setIsRunning
}: CellInputProps) {
  const [inputValue, setInputValue] = useState('')

  const handleSubmitInput = async () => {
    try {
      await onSubmitInput(inputValue)
      setInputValue('')
      setNeedsInput(false)
      setIsRunning(false)
    } catch (error) {
      console.error('Error submitting input:', error)
      toast.error('Error submitting input')
      setNeedsInput(false)
      setIsRunning(false)
    }
  }

  if (!needsInput) return null

  return (
    <div className="p-2 border-t bg-muted/20">
      <div className="text-xs text-muted-foreground mb-2">{inputPrompt}</div>
      <div className="flex gap-2">
        <Input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="Enter your input..."
          className="text-xs h-7"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleSubmitInput()
            }
          }}
          autoFocus
        />
        <Button
          size="sm"
          onClick={handleSubmitInput}
          className="h-7 px-2"
        >
          <Send className="h-3 w-3" />
        </Button>
      </div>
    </div>
  )
}
