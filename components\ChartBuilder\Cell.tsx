'use client'

import { useState, useRef, useEffect, useMemo } from 'react'
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Play, Trash2, Plus, Database, ChevronDown, Loader2, Code2, Server, BarChart3, Maximize2, MoveUp, MoveDown, Bold, Italic, List, ListOrdered, Code, Link, Image as ImageIcon, Table as TableIcon, Square, FileText } from "lucide-react"
import Editor from '@monaco-editor/react'
import { useTheme } from 'next-themes'
import { toast } from 'sonner'
import { Table } from "@/components/ui/table"
import { Dataset } from '@/types/index'
import { QueryResult } from './QueryResult'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"
import { cn } from "@/lib/utils"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { formatDistanceToNow } from 'date-fns'
import _ from 'lodash'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"
import { MarkdownCell, MarkdownCellRef } from './MarkdownCell'
import { useKeyboardShortcuts, KeyboardShortcutHandlers } from './hooks/useKeyboardShortcuts'
import { FullscreenEditor } from './FullscreenEditor'
import { CellToolbar } from './cell/CellToolbar'
import { useCellHandlers } from './cell/CellHandlers'
import { CellStatus } from './cell/CellStatus'
import { CellInput } from './cell/CellInput'


interface ServerStatus {
  status: 'healthy' | 'error' | 'warning'
  message: string
}

interface CellProps {
  id: string
  content: string
  result?: {
    data: any[];
    output?: string;
    plots?: string[];
    error?: string;
    errorDetails?: {
      message: string;
      code?: string;
      stack?: string;
      serverTrace?: string;
    };
    executionTime?: number;
    result?: any;
    needs_input?: boolean;
    input_prompt?: string;
  };
  onRun: (id: string, code: string, shouldShowGraphicWalker?: boolean) => Promise<any>
  onDelete: (id: string) => void
  onAddCell: (id: string, cellType?: 'code' | 'markdown') => void
  onSelectDatasets: (ids: string[]) => Promise<{ selectedList: Dataset[], datasetIds: string[] } | void>
  language: string
  onLanguageChange: (language: string) => void
  isSuccess?: boolean
  selectedDatasets: Dataset[]
  availableDatasets: Dataset[]
  showGraphicWalker?: boolean
  onSaveChart?: (data: any[], config: any, chartType: 'line' | 'bar' | 'pie' | 'area', chartId?: string) => void
  onSaveTable?: (data: any[], columns: string[], tableId?: string) => void
  onSavePlot?: (plotUrl: string, plotTitle: string, plotId?: string) => void
  lastRun?: Date;
  lastUpdate?: Date;
  chartType?: 'line' | 'bar' | 'pie' | 'area'
  onChartTypeChange?: (type: 'line' | 'bar' | 'pie' | 'area') => void
  onContentChange?: (value: string) => void
  viewMode?: 'table' | 'chart' | 'output' | 'graphicwalker'
  onViewModeChange?: (mode: 'table' | 'chart' | 'output' | 'graphicwalker') => void
  notes?: string;
  onUpdateNotes?: (id: string, notes: string) => void;
  cellType?: 'code' | 'markdown';
  onConvertCellType?: (id: string, targetType: 'code' | 'markdown') => void;
  index?: number;
  dragHandleProps?: any;
  onMoveUp?: (id: string) => void;
  onMoveDown?: (id: string) => void;
  onUpdateResult?: (id: string, result: any) => void; // Add this for direct result updates
}

// Add this function to check if the SQL query has the --#graphicwalker comment
function hasGraphicWalkerComment(code: string): boolean {
  return code.includes("--#graphicwalker") || code.includes("-- #graphicwalker");
}

// Add this function to check if the SQL query has the loopchart command
function hasLoopchartCommand(code: string): boolean {
  return code.includes("--loopchart") || code.includes("-- loopchart");
}

export function Cell({
  id,
  content,
  result,
  onRun,
  onDelete,
  onAddCell,
  onSelectDatasets,
  language,
  onLanguageChange,
  isSuccess,
  selectedDatasets,
  availableDatasets,
  showGraphicWalker,
  onSaveChart,
  onSaveTable,
  onSavePlot,
  chartType,
  onChartTypeChange,
  onContentChange,
  viewMode,
  onViewModeChange,
  notes = '[]',
  onUpdateNotes,
  index,
  dragHandleProps,
  onMoveUp,
  onMoveDown,
  cellType = 'code',
  onConvertCellType,
  onUpdateResult
}: CellProps) {
  const { theme } = useTheme()
  const [isHovered, setIsHovered] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  const [isLoadingDatasets, setIsLoadingDatasets] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [editorHeight, setEditorHeight] = useState('50px')
  const [serverStatus, setServerStatus] = useState<ServerStatus>({
    status: 'warning',
    message: 'Checking server status...'
  })
  const editorRef = useRef<any>(null)
  const markdownCellRef = useRef<MarkdownCellRef>(null)
  const [executionTime, setExecutionTime] = useState<{
    startTime?: Date;
    endTime?: Date;
  }>({})
  const [currentExecutionTime, setCurrentExecutionTime] = useState<number>(0)
  const executionTimerRef = useRef<NodeJS.Timeout | null>(null)
  const [isFullscreenOpen, setIsFullscreenOpen] = useState(false)
  const [needsInput, setNeedsInput] = useState(false)
  const [inputPrompt, setInputPrompt] = useState('')
  const [userInput, setUserInput] = useState('')
  const [originalCode, setOriginalCode] = useState('')



  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Watch for input requirements in the result
  useEffect(() => {
    if (result?.needs_input) {
      setNeedsInput(true)
      setInputPrompt(result.input_prompt || 'Enter input:')
      // Auto-switch to output view to show the input field
      if (onViewModeChange) {
        onViewModeChange('output')
      }
    } else {
      setNeedsInput(false)
      setInputPrompt('')
    }
  }, [result?.needs_input, result?.input_prompt, onViewModeChange])

  // Auto-switch view based on content type (only on new results, not on every render)
  const [lastResultTimestamp, setLastResultTimestamp] = useState<number>(0)

  useEffect(() => {
    if (language === 'python' && onViewModeChange && result) {
      // Only auto-switch when we have a new result (not on every render)
      const currentTimestamp = result.executionTime || Date.now()
      if (currentTimestamp <= lastResultTimestamp) {
        return // Don't auto-switch for the same result
      }

      setLastResultTimestamp(currentTimestamp)

      console.log('🔍 Cell auto-switch check for new result:', {
        hasData: !!result.data,
        dataLength: result.data?.length,
        hasOutput: !!result.output,
        outputType: result.outputType,
        resultType: typeof result.result,
        hasDataFrameResult: result.result && typeof result.result === 'object' && result.result.type === 'table',
        columns: result.columns
      });

      // Only auto-switch if no manual view mode has been set
      // Priority 1: DataFrame data (from variable assignments like result = df)
      if (result.data && Array.isArray(result.data) && result.data.length > 0) {
        console.log('📊 Auto-switching to table view for DataFrame data');
        onViewModeChange('table')
      }
      // Priority 2: Table result objects (from DataFrame expressions)
      else if (result.result && typeof result.result === 'object' &&
               (result.result.type === 'table' || result.result.data)) {
        console.log('📊 Auto-switching to table view for table result');
        onViewModeChange('table')
      }
      // Priority 3: Text output or interactive input needed
      else if (result.output || result.needs_input) {
        console.log('📝 Auto-switching to output view for text output or input');
        onViewModeChange('output')
      }
      // Priority 4: Plots available
      else if (result.plots && result.plots.length > 0) {
        console.log('🎨 Auto-switching to output view for plots');
        onViewModeChange('output')
      }
    }
  }, [result]) // Only depend on result changes, not on viewMode

  // Enhanced input submission with proper code handling and result updating
  const handleInputSubmit = async () => {
    if (!userInput.trim()) return;

    // Get the current code from the editor or use the stored original code
    const codeToExecute = originalCode || editorRef.current?.getValue() || content;

    if (!codeToExecute.trim()) {
      toast.error('No code available to execute with input');
      return;
    }

    console.log('🔄 Submitting input:', {
      userInput: userInput.trim(),
      codeLength: codeToExecute.length,
      hasOriginalCode: !!originalCode
    });

    try {
      setIsRunning(true);
      const startTime = new Date();
      setExecutionTime({ startTime, endTime: undefined });

      // For input continuation, use optimized approach
      const isInputContinuation = needsInput && originalCode;

      const response = await fetch('/api/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: isInputContinuation ? '' : codeToExecute, // Empty code for continuation
          language: 'python',
          datasets: selectedDatasets.map(ds => ({
            id: ds.id,
            name: ds.name,
            data: ds.data
          })),
          user_input: userInput.trim() // Send the user input
        }),
      });

      if (response.ok) {
        const responseData = await response.json();
        console.log('✅ Input submission successful:', responseData);

        // **CRITICAL FIX: Use the response data directly to update the cell result**
        // Instead of calling onRun again, we use the response we already have

        if (onUpdateResult) {
          try {
            // Transform the response data to match the expected result format
            const updatedResult = {
              data: responseData.data || [],
              output: responseData.output || '',
              plots: responseData.plots || [],
              error: responseData.error,
              errorDetails: responseData.errorDetails,
              executionTime: responseData.executionTime,
              result: responseData.result,
              needs_input: responseData.needs_input || false,
              input_prompt: responseData.input_prompt || '',
              variables: responseData.variables,
              variableTypes: responseData.variableTypes,
              html_outputs: responseData.html_outputs || []
            };

            // Update the cell result directly with the response
            onUpdateResult(id, updatedResult);
            console.log('📝 Cell result updated with input response:', updatedResult);
          } catch (error) {
            console.error('Error updating cell result:', error);
          }
        } else {
          // Fallback: if onUpdateResult is not available, use onRun
          console.log('⚠️ onUpdateResult not available, falling back to onRun');
          if (onRun) {
            try {
              await onRun(id, codeToExecute, false);
            } catch (error) {
              console.error('Error updating cell result:', error);
            }
          }
        }

        // Clear input state
        setUserInput('');
        setNeedsInput(false);
        setInputPrompt('');
        setOriginalCode('');

        const endTime = new Date();
        setExecutionTime({ startTime, endTime });
        setIsRunning(false);

        toast.success('Input submitted successfully');
      } else {
        const errorData = await response.json();
        console.error('❌ Input submission failed:', errorData);
        toast.error(`Failed to submit input: ${errorData.error || 'Unknown error'}`);
        setNeedsInput(false);
        setIsRunning(false);
      }
    } catch (error) {
      console.error('❌ Error submitting input:', error);
      toast.error('Error submitting input');
      setNeedsInput(false);
      setIsRunning(false);
    }
  }
  const [isEditingMarkdown, setIsEditingMarkdown] = useState(false)
  const [windowWidth, setWindowWidth] = useState<number>(typeof window !== 'undefined' ? window.innerWidth : 0)

  // Language cycling for shortcuts
  const languages = ['sql', 'python', 'javascript'];
  const currentLanguageIndex = languages.indexOf(language);

  // Check if data is available for chart view
  const hasChartableData = result?.data && result.data.length > 0 && Object.keys(result.data[0] || {}).some(
    key => typeof result.data[0][key] === 'number'
  )

  // Window resize handler
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    const handleSidebarToggle = () => {
      // Small delay to allow sidebar animation to complete
      setTimeout(() => {
        setWindowWidth(window.innerWidth)
      }, 300)
    }

    window.addEventListener('resize', handleResize);
    window.addEventListener('sidebar-toggle', handleSidebarToggle);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('sidebar-toggle', handleSidebarToggle);
    };
  }, []);

  // Server status check - Check Python backend health
  useEffect(() => {
    const checkServerStatus = async () => {
      try {
        // Check Python backend health
        const response = await fetch('https://flopbackend.onrender.com/', {
          method: 'GET',
          mode: 'cors'
        })
        if (response.ok) {
          const data = await response.json()
          if (data.message && data.status === 'running') {
            setServerStatus({ status: 'healthy', message: 'Python backend is running' })
          } else {
            setServerStatus({ status: 'warning', message: 'Backend response unexpected' })
          }
        } else {
          setServerStatus({ status: 'error', message: 'Python backend error' })
        }
      } catch (error) {
        setServerStatus({ status: 'error', message: 'Python backend unreachable' })
      }
    }

    checkServerStatus()
    const interval = setInterval(checkServerStatus, 30000) // Check every 30 seconds
    return () => clearInterval(interval)
  }, [])

  // Auto-adjust editor height based on content
  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor

    const updateHeight = () => {
      const contentHeight = Math.min(1000, Math.max(50, editor.getContentHeight()))
      setEditorHeight(`${contentHeight}px`)
    }

    editor.onDidContentSizeChange(updateHeight)
    updateHeight()
  }

  // Update editor content when content prop changes (from fullscreen save)
  useEffect(() => {
    if (editorRef.current && content !== editorRef.current.getValue()) {
      editorRef.current.setValue(content)
    }
  }, [content])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      // Check if the click is outside the dropdown container
      if (!target.closest(`[data-dropdown-id="${id}"]`)) {
        setIsDropdownOpen(false)
      }
    }

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isDropdownOpen, id])


  // Use extracted handlers
  const {
    handleDropdownToggle,
    handleDatasetSelection,
    handleRunCell,
    handleLanguageChange,
    debouncedContentChange
  } = useCellHandlers({
    id,
    language,
    onLanguageChange,
    onSelectDatasets,
    onRun,
    onContentChange,
    editorRef,
    setIsLoadingDatasets,
    setIsDropdownOpen,
    setIsRunning,
    setExecutionTime,
    setCurrentExecutionTime,
    executionTimerRef,
    setNeedsInput,
    setInputPrompt,
    onViewModeChange
  })



  // Keyboard shortcut handlers
  const keyboardHandlers: KeyboardShortcutHandlers = {
    onRun: handleRunCell,
    onDelete: () => onDelete(id),
    onAddCellAbove: () => onAddCell(id, 'code'), // Add above current cell
    onAddCellBelow: () => onAddCell(id, 'code'), // Add below current cell
    onMoveUp: onMoveUp ? () => onMoveUp(id) : undefined,
    onMoveDown: onMoveDown ? () => onMoveDown(id) : undefined,
    onConvertToMarkdown: onConvertCellType ? () => onConvertCellType(id, 'markdown') : undefined,
    onConvertToCode: onConvertCellType ? () => onConvertCellType(id, 'code') : undefined,
    onFocusEditor: () => {
      if (editorRef.current) {
        editorRef.current.focus();
      }
    },
    onToggleLanguage: () => {
      if (cellType === 'code') {
        const nextIndex = (currentLanguageIndex + 1) % languages.length;
        onLanguageChange(languages[nextIndex]);
      }
    }
  };

  // Use keyboard shortcuts hook
  useKeyboardShortcuts(keyboardHandlers, {
    cellId: id,
    isHovered,
    isRunning,
    cellType,
    language,
    editorRef,
    disabled: false
  });

  // In the Cell component, update the editor onChange handler to use debouncing
  const debouncedContentChange = useRef(
    _.debounce((value: string) => {
      if (onContentChange) {
        onContentChange(value);
      }
    }, 200)  // 200ms debounce
  ).current;

  // Add a useEffect for cleanup
  useEffect(() => {
    return () => {
      // Clean up the timeout when component unmounts
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      // Clean up the execution timer
      if (executionTimerRef.current) {
        clearInterval(executionTimerRef.current);
      }
    };
  }, []);

  // Handle notes update
  const handleNotesUpdate = (newNotes: string) => {
    if (onUpdateNotes) {
      onUpdateNotes(id, newNotes);
    }
  };

  // Handle chart type change and auto-switch to chart view
  const handleChartTypeChange = (newType: 'line' | 'bar' | 'pie' | 'area') => {
    if (onChartTypeChange) {
      onChartTypeChange(newType);
      
      // Auto-switch to chart view when chart type is changed
      if (onViewModeChange && hasChartableData) {
        onViewModeChange('chart');
      }
    }
  };

  return (
    <div
      className="relative group my-2"
      data-cell-id={id}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Add Cell Button - Now positioned at the bottom */}
      <div className={cn(
        "absolute -bottom-2 left-1/2 transform -translate-x-1/2 z-20",
        isHovered ? "opacity-100" : "opacity-0"
      )}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-5 rounded-full p-0 bg-background hover:bg-accent px-1"
            >
              <Plus className="h-3 w-3 mr-0.5" />
              <span className="text-[9px]">Add</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="center">
            <DropdownMenuItem onClick={() => onAddCell(id, 'code')}>
              <Code2 className="h-3 w-3 mr-2" />
              <span className="text-xs">Code Cell</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onAddCell(id, 'markdown')}>
              <FileText className="h-3 w-3 mr-2" />
              <span className="text-xs">Markdown Cell</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Card
        className={cn(
          "relative w-full overflow-hidden transition-all duration-300 py-0",
          cellType === 'markdown'
            ? "bg-transparent border-none shadow-none"
            : cn(
                "border",
                isHovered
                  ? (result?.error ? "border-red-500" : isSuccess ? "border-green-500" : "border-primary/50")
                  : "border-border",
                "shadow-sm"
              )
        )}
        style={{
          width: '100%',
          maxWidth: '100%',
        }}
        data-cell-id={id}
      >
        {/* Cell Number Indicator - No drag functionality */}
        <div
          className={cn(
            "absolute left-0 top-0 z-20 flex items-center justify-center h-6 w-6 rounded-br-md text-xs font-mono font-semibold",
            cellType === 'markdown' ? "text-muted-foreground/50" : ""
          )}
        >
          {typeof index === 'number' ? `[${index + 1}]` : '•'}
        </div>
        {/* Cell Toolbar - Extracted Component */}
        <CellToolbar
          cellType={cellType}
          isHovered={isHovered}
          language={language}
          onLanguageChange={handleLanguageChange}
          selectedDatasets={selectedDatasets}
          availableDatasets={availableDatasets}
          isDropdownOpen={isDropdownOpen}
          handleDropdownToggle={handleDropdownToggle}
          handleDatasetSelection={handleDatasetSelection}
          serverStatus={serverStatus}
          isRunning={isRunning}
          currentExecutionTime={currentExecutionTime}
          handleRunCell={handleRunCell}
          onMoveUp={onMoveUp}
          onMoveDown={onMoveDown}
          onConvertCellType={onConvertCellType}
          isEditingMarkdown={isEditingMarkdown}
          setIsEditingMarkdown={setIsEditingMarkdown}
          setIsFullscreenOpen={setIsFullscreenOpen}
          onDelete={onDelete}
          onContentChange={onContentChange}
          editorRef={editorRef}
          id={id}
          markdownCellRef={markdownCellRef}
        />



        {/* Cell Status - Extracted Component */}
        <CellStatus
          selectedDatasets={selectedDatasets}
          cellType={cellType}
          executionTime={executionTime}
          isRunning={isRunning}
          currentExecutionTime={currentExecutionTime}
        />

        <div className="transition-colors duration-200">
          {/* Editor Area - Adjusted padding for cell number only */}
          <div className="p-0 pl-6"> {/* Add left padding to accommodate cell number */}
            {cellType === 'markdown' ? (
              <MarkdownCell
                ref={markdownCellRef}
                content={content}
                onContentChange={onContentChange}
                isEditing={isEditingMarkdown}
                setIsEditing={setIsEditingMarkdown}
              />
            ) : (
              <Editor
                height={editorHeight}
                defaultLanguage={language}
                defaultValue={content || ''}
                theme={theme === 'dark' ? 'vs-dark' : 'light'}
                options={{
                  minimap: { enabled: false },
                  lineNumbers: 'on',
                  folding: false,
                  scrollBeyondLastLine: false,
                  wordWrap: 'on',
                  contextmenu: false,
                  fontSize: 11,
                  lineHeight: 15,
                  padding: { top: 1, bottom: 1 },
                  // Enhanced syntax highlighting
                  bracketPairColorization: { enabled: true },
                  colorDecorators: true,
                  // Better suggestions
                  quickSuggestions: {
                    other: true,
                    comments: false,
                    strings: false
                  },
                  suggestOnTriggerCharacters: true,
                  acceptSuggestionOnCommitCharacter: true,
                  acceptSuggestionOnEnter: 'on',
                  tabCompletion: 'on',
                  // Code formatting
                  formatOnPaste: true,
                  formatOnType: true,
                  // Visual enhancements
                  cursorBlinking: 'smooth',
                  cursorSmoothCaretAnimation: 'on',
                  smoothScrolling: true,
                  renderLineHighlight: 'all',
                  renderWhitespace: 'selection',
                  // Language features
                  hover: { enabled: true },
                  parameterHints: { enabled: true },
                  // Selection and highlighting
                  selectionHighlight: true,
                  occurrencesHighlight: 'singleFile',
                }}
                onMount={handleEditorDidMount}
                onChange={(value) => {
                  if (typeof value === 'string') {
                    debouncedContentChange(value);
                  }
                }}
              />
            )}
          </div>

          {/* Results - More compact */}
          <div className={cn("mt-0 relative", result ? "border-t border-t-border/50" : "")} data-cell-id={id}>
            {result && cellType === 'code' && (
              <QueryResult
                data={result.data}
                output={result.output}
                plots={result.plots}
                result={result.result}
                error={result.error}
                errorDetails={result.errorDetails}
                executionTime={result.executionTime}
                isSuccess={isSuccess && !result.error}
                showGraphicWalker={showGraphicWalker}
                onSaveChart={onSaveChart}
                onSaveTable={onSaveTable}
                onSavePlot={onSavePlot}
                chartType={chartType}
                onChartTypeChange={onChartTypeChange}
                viewMode={viewMode}
                onViewModeChange={onViewModeChange}
                cellId={id}
                language={language} // Pass language to QueryResult
                // Pass cell control props
                onMoveUp={onMoveUp}
                onMoveDown={onMoveDown}
                notes={notes}
                onUpdateNotes={onUpdateNotes}
                needsInput={result?.needs_input || needsInput}
                inputPrompt={result?.input_prompt || inputPrompt}
                onInputSubmit={async (input: string) => {
                  setUserInput(input);
                  await handleInputSubmit();
                }}
              />
            )}

          </div>
        </div>
      </Card>



      {/* Fullscreen Editor */}
      <FullscreenEditor
        isOpen={isFullscreenOpen}
        onClose={() => setIsFullscreenOpen(false)}
        content={content}
        language={language}
        onContentChange={onContentChange || (() => {})}
        onRun={cellType === 'code' ? handleRunCell : undefined}
        isRunning={isRunning}
        cellId={`${index !== undefined ? index + 1 : '?'}`}
      />

      {/* Input Dialog for Python interactive input */}

    </div>
  );
}