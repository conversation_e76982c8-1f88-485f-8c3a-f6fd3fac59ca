'use client'

import { useState, useRef, useEffect, useMemo } from 'react'
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Play, Trash2, Plus, Database, ChevronDown, Loader2, Code2, Server, BarChart3, Maximize2, MoveUp, MoveDown, Bold, Italic, List, ListOrdered, Code, Link, Image as ImageIcon, Table as TableIcon, Square, FileText } from "lucide-react"
import Editor from '@monaco-editor/react'
import { useTheme } from 'next-themes'
import { toast } from 'sonner'
import { Table } from "@/components/ui/table"
import { Dataset } from '@/types/index'
import { QueryResult } from './QueryResult'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"
import { cn } from "@/lib/utils"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { formatDistanceToNow } from 'date-fns'
import _ from 'lodash'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"
import { MarkdownCell, MarkdownCellRef } from './MarkdownCell'
import { AIAssistant } from './AIAssistant'
import { useKeyboardShortcuts, KeyboardShortcutHandlers } from './hooks/useKeyboardShortcuts'
import { KeyboardShortcutsHelp } from './KeyboardShortcutsHelp'
import { FullscreenEditor } from './FullscreenEditor'
import { Checkbox } from '../ui/checkbox'


interface ServerStatus {
  status: 'healthy' | 'error' | 'warning'
  message: string
}

interface CellProps {
  id: string
  content: string
  result?: {
    data: any[];
    output?: string;
    plots?: string[];
    error?: string;
    errorDetails?: {
      message: string;
      code?: string;
      stack?: string;
      serverTrace?: string;
    };
    executionTime?: number;
    result?: any;
    needs_input?: boolean;
    input_prompt?: string;
  };
  onRun: (id: string, code: string, shouldShowGraphicWalker?: boolean) => Promise<any>
  onDelete: (id: string) => void
  onAddCell: (id: string, cellType?: 'code' | 'markdown') => void
  onSelectDatasets: (ids: string[]) => Promise<{ selectedList: Dataset[], datasetIds: string[] } | void>
  language: string
  onLanguageChange: (language: string) => void
  isSuccess?: boolean
  selectedDatasets: Dataset[]
  availableDatasets: Dataset[]
  showGraphicWalker?: boolean
  onSaveChart?: (data: any[], config: any, chartType: 'line' | 'bar' | 'pie' | 'area', chartId?: string) => void
  onSaveTable?: (data: any[], columns: string[], tableId?: string) => void
  onSavePlot?: (plotUrl: string, plotTitle: string, plotId?: string) => void
  lastRun?: Date;
  lastUpdate?: Date;
  chartType?: 'line' | 'bar' | 'pie' | 'area'
  onChartTypeChange?: (type: 'line' | 'bar' | 'pie' | 'area') => void
  onContentChange?: (value: string) => void
  viewMode?: 'table' | 'chart' | 'output' | 'graphicwalker'
  onViewModeChange?: (mode: 'table' | 'chart' | 'output' | 'graphicwalker') => void
  notes?: string;
  onUpdateNotes?: (id: string, notes: string) => void;
  cellType?: 'code' | 'markdown';
  onConvertCellType?: (id: string, targetType: 'code' | 'markdown') => void;
  index?: number;
  dragHandleProps?: any;
  onMoveUp?: (id: string) => void;
  onMoveDown?: (id: string) => void;
  onUpdateResult?: (id: string, result: any) => void; // Add this for direct result updates
}

// Add this function to check if the SQL query has the --#graphicwalker comment
function hasGraphicWalkerComment(code: string): boolean {
  return code.includes("--#graphicwalker") || code.includes("-- #graphicwalker");
}

// Add this function to check if the SQL query has the loopchart command
function hasLoopchartCommand(code: string): boolean {
  return code.includes("--loopchart") || code.includes("-- loopchart");
}

export function Cell({
  id,
  content,
  result,
  onRun,
  onDelete,
  onAddCell,
  onSelectDatasets,
  language,
  onLanguageChange,
  isSuccess,
  selectedDatasets,
  availableDatasets,
  showGraphicWalker,
  onSaveChart,
  onSaveTable,
  onSavePlot,
  chartType,
  onChartTypeChange,
  onContentChange,
  viewMode,
  onViewModeChange,
  notes = '[]',
  onUpdateNotes,
  index,
  dragHandleProps,
  onMoveUp,
  onMoveDown,
  cellType = 'code',
  onConvertCellType,
  onUpdateResult
}: CellProps) {
  const { theme } = useTheme()
  const [isHovered, setIsHovered] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  const [isLoadingDatasets, setIsLoadingDatasets] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [editorHeight, setEditorHeight] = useState('50px')
  const [serverStatus, setServerStatus] = useState<ServerStatus>({
    status: 'warning',
    message: 'Checking server status...'
  })
  const editorRef = useRef<any>(null)
  const markdownCellRef = useRef<MarkdownCellRef>(null)
  const [executionTime, setExecutionTime] = useState<{
    startTime?: Date;
    endTime?: Date;
  }>({})
  const [currentExecutionTime, setCurrentExecutionTime] = useState<number>(0)
  const executionTimerRef = useRef<NodeJS.Timeout | null>(null)
  const [isFullscreenOpen, setIsFullscreenOpen] = useState(false)
  const [needsInput, setNeedsInput] = useState(false)
  const [inputPrompt, setInputPrompt] = useState('')
  const [userInput, setUserInput] = useState('')
  const [originalCode, setOriginalCode] = useState('')



  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Watch for input requirements in the result
  useEffect(() => {
    if (result?.needs_input) {
      setNeedsInput(true)
      setInputPrompt(result.input_prompt || 'Enter input:')
      // Auto-switch to output view to show the input field
      if (onViewModeChange) {
        onViewModeChange('output')
      }
    } else {
      setNeedsInput(false)
      setInputPrompt('')
    }
  }, [result?.needs_input, result?.input_prompt, onViewModeChange])

  // Auto-switch view based on content type (only on new results, not on every render)
  const [lastResultTimestamp, setLastResultTimestamp] = useState<number>(0)

  useEffect(() => {
    if (language === 'python' && onViewModeChange && result) {
      // Only auto-switch when we have a new result (not on every render)
      const currentTimestamp = result.executionTime || Date.now()
      if (currentTimestamp <= lastResultTimestamp) {
        return // Don't auto-switch for the same result
      }

      setLastResultTimestamp(currentTimestamp)

      console.log('🔍 Cell auto-switch check for new result:', {
        hasData: !!result.data,
        dataLength: result.data?.length,
        hasOutput: !!result.output,
        outputType: result.outputType,
        resultType: typeof result.result,
        hasDataFrameResult: result.result && typeof result.result === 'object' && result.result.type === 'table',
        columns: result.columns
      });

      // Only auto-switch if no manual view mode has been set
      // Priority 1: DataFrame data (from variable assignments like result = df)
      if (result.data && Array.isArray(result.data) && result.data.length > 0) {
        console.log('📊 Auto-switching to table view for DataFrame data');
        onViewModeChange('table')
      }
      // Priority 2: Table result objects (from DataFrame expressions)
      else if (result.result && typeof result.result === 'object' &&
               (result.result.type === 'table' || result.result.data)) {
        console.log('📊 Auto-switching to table view for table result');
        onViewModeChange('table')
      }
      // Priority 3: Text output or interactive input needed
      else if (result.output || result.needs_input) {
        console.log('📝 Auto-switching to output view for text output or input');
        onViewModeChange('output')
      }
      // Priority 4: Plots available
      else if (result.plots && result.plots.length > 0) {
        console.log('🎨 Auto-switching to output view for plots');
        onViewModeChange('output')
      }
    }
  }, [result]) // Only depend on result changes, not on viewMode

  // Enhanced input submission with proper code handling and result updating
  const handleInputSubmit = async () => {
    if (!userInput.trim()) return;

    // Get the current code from the editor or use the stored original code
    const codeToExecute = originalCode || editorRef.current?.getValue() || content;

    if (!codeToExecute.trim()) {
      toast.error('No code available to execute with input');
      return;
    }

    console.log('🔄 Submitting input:', {
      userInput: userInput.trim(),
      codeLength: codeToExecute.length,
      hasOriginalCode: !!originalCode
    });

    try {
      setIsRunning(true);
      const startTime = new Date();
      setExecutionTime({ startTime, endTime: undefined });

      // For input continuation, use optimized approach
      const isInputContinuation = needsInput && originalCode;

      const response = await fetch('/api/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: isInputContinuation ? '' : codeToExecute, // Empty code for continuation
          language: 'python',
          datasets: selectedDatasets.map(ds => ({
            id: ds.id,
            name: ds.name,
            data: ds.data
          })),
          user_input: userInput.trim() // Send the user input
        }),
      });

      if (response.ok) {
        const responseData = await response.json();
        console.log('✅ Input submission successful:', responseData);

        // **CRITICAL FIX: Use the response data directly to update the cell result**
        // Instead of calling onRun again, we use the response we already have

        if (onUpdateResult) {
          try {
            // Transform the response data to match the expected result format
            const updatedResult = {
              data: responseData.data || [],
              output: responseData.output || '',
              plots: responseData.plots || [],
              error: responseData.error,
              errorDetails: responseData.errorDetails,
              executionTime: responseData.executionTime,
              result: responseData.result,
              needs_input: responseData.needs_input || false,
              input_prompt: responseData.input_prompt || '',
              variables: responseData.variables,
              variableTypes: responseData.variableTypes,
              html_outputs: responseData.html_outputs || []
            };

            // Update the cell result directly with the response
            onUpdateResult(id, updatedResult);
            console.log('📝 Cell result updated with input response:', updatedResult);
          } catch (error) {
            console.error('Error updating cell result:', error);
          }
        } else {
          // Fallback: if onUpdateResult is not available, use onRun
          console.log('⚠️ onUpdateResult not available, falling back to onRun');
          if (onRun) {
            try {
              await onRun(id, codeToExecute, false);
            } catch (error) {
              console.error('Error updating cell result:', error);
            }
          }
        }

        // Clear input state
        setUserInput('');
        setNeedsInput(false);
        setInputPrompt('');
        setOriginalCode('');

        const endTime = new Date();
        setExecutionTime({ startTime, endTime });
        setIsRunning(false);

        toast.success('Input submitted successfully');
      } else {
        const errorData = await response.json();
        console.error('❌ Input submission failed:', errorData);
        toast.error(`Failed to submit input: ${errorData.error || 'Unknown error'}`);
        setNeedsInput(false);
        setIsRunning(false);
      }
    } catch (error) {
      console.error('❌ Error submitting input:', error);
      toast.error('Error submitting input');
      setNeedsInput(false);
      setIsRunning(false);
    }
  }
  const [isEditingMarkdown, setIsEditingMarkdown] = useState(false)
  const [windowWidth, setWindowWidth] = useState<number>(typeof window !== 'undefined' ? window.innerWidth : 0)

  // Language cycling for shortcuts
  const languages = ['sql', 'python', 'javascript'];
  const currentLanguageIndex = languages.indexOf(language);

  // Check if data is available for chart view
  const hasChartableData = result?.data && result.data.length > 0 && Object.keys(result.data[0] || {}).some(
    key => typeof result.data[0][key] === 'number'
  )

  // Window resize handler
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    const handleSidebarToggle = () => {
      // Small delay to allow sidebar animation to complete
      setTimeout(() => {
        setWindowWidth(window.innerWidth)
      }, 300)
    }

    window.addEventListener('resize', handleResize);
    window.addEventListener('sidebar-toggle', handleSidebarToggle);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('sidebar-toggle', handleSidebarToggle);
    };
  }, []);

  // Server status check - Check Python backend health
  useEffect(() => {
    const checkServerStatus = async () => {
      try {
        // Check Python backend health
        const response = await fetch('https://flopbackend.onrender.com/', {
          method: 'GET',
          mode: 'cors'
        })
        if (response.ok) {
          const data = await response.json()
          if (data.message && data.status === 'running') {
            setServerStatus({ status: 'healthy', message: 'Python backend is running' })
          } else {
            setServerStatus({ status: 'warning', message: 'Backend response unexpected' })
          }
        } else {
          setServerStatus({ status: 'error', message: 'Python backend error' })
        }
      } catch (error) {
        setServerStatus({ status: 'error', message: 'Python backend unreachable' })
      }
    }

    checkServerStatus()
    const interval = setInterval(checkServerStatus, 30000) // Check every 30 seconds
    return () => clearInterval(interval)
  }, [])

  // Auto-adjust editor height based on content
  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor

    const updateHeight = () => {
      const contentHeight = Math.min(1000, Math.max(50, editor.getContentHeight()))
      setEditorHeight(`${contentHeight}px`)
    }

    editor.onDidContentSizeChange(updateHeight)
    updateHeight()
  }

  // Update editor content when content prop changes (from fullscreen save)
  useEffect(() => {
    if (editorRef.current && content !== editorRef.current.getValue()) {
      editorRef.current.setValue(content)
    }
  }, [content])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      // Check if the click is outside the dropdown container
      if (!target.closest(`[data-dropdown-id="${id}"]`)) {
        setIsDropdownOpen(false)
      }
    }

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isDropdownOpen, id])


  // Dataset dropdown handlers
  const handleDropdownToggle = () => {
    setIsDropdownOpen(!isDropdownOpen)
  }

  const handleDatasetSelection = async (datasetIds: string[]) => {
    setIsLoadingDatasets(true)
    try {
      // Call the parent callback - this should update the selectedDatasets prop
      const result = await onSelectDatasets(datasetIds)

      console.log('Datasets selected:', datasetIds)
      toast.success(`Selected ${datasetIds.length} dataset${datasetIds.length !== 1 ? 's' : ''}`)
    } catch (error) {
      console.error('Error selecting datasets:', error)
      toast.error('Failed to load datasets')
    } finally {
      setIsLoadingDatasets(false)
      setIsDropdownOpen(false)
    }
  }

  const handleRunCell = async () => {
    if (isRunning) return;

    const code = editorRef.current?.getValue() || content;
    
    // Validation checks
    if (!code.trim()) {
      toast.error('Please enter some code to execute');
      return;
    }

    // Check for language mismatch
    if (language === 'python' && code.trim().startsWith('SELECT')) {
      toast.error('You are using SQL syntax with Python selected. Please change to SQL or update your code.');
      return;
    }

    if (language === 'sql' && (code.includes('import ') || code.includes('def ') || code.includes('print('))) {
      toast.error('You are using Python syntax with SQL selected. Please change to Python or update your code.');
      return;
    }

    if (language === 'python') {
      // Check for common SQL patterns in Python mode - but be more lenient with comments
      const codeLines = code.split('\n').map((line: string) => line.trim()).filter((line: string) => line && !line.startsWith('#'));
      const hasSQL = codeLines.some((line: string) =>
        line.startsWith('SELECT') ||
        line.startsWith('FROM dataset') ||
        line.includes('SELECT *') ||
        (line.startsWith('--') && !line.includes('python') && !line.includes('docs'))
      );

      if (hasSQL) {
        toast.error('You are using SQL syntax with Python selected. Please change to SQL or update your code.');
        return;
      }

      // For matplotlib plots, ensure the code includes plt.show() if it uses plt
      if (code.includes('plt.') && !code.includes('plt.show()') && !code.includes('.plot(')) {
        // If the code uses matplotlib but doesn't call plt.show(), show a helpful tip
        toast.info('Tip: Add plt.show() at the end to display your matplotlib plot.');
      }
    }

    setIsRunning(true);
    const startTime = new Date();
    setExecutionTime({ startTime, endTime: undefined });
    setCurrentExecutionTime(0);

    // Start live execution timer (like Jupyter) with better feedback
    executionTimerRef.current = setInterval(() => {
      const elapsed = Date.now() - startTime.getTime();
      setCurrentExecutionTime(elapsed);

      // Provide feedback for long-running operations
      if (elapsed > 5000 && elapsed % 5000 < 100) {
        console.log(`⏱️ Code still executing... ${(elapsed / 1000).toFixed(1)}s`);
      }
    }, 100); // Update every 100ms for good balance

    // Check for GraphicWalker comment or loopchart command
    const shouldShowGraphicWalker = language === 'sql' &&
      (hasGraphicWalkerComment(code) || hasLoopchartCommand(code));

    try {
      // For Python, ensure we have a valid code that won't cause syntax errors
      let codeToRun = code;
      if (language === 'python' && code.trim().startsWith('--')) {
        // If somehow we still have SQL comments in Python mode, convert them to Python comments
        codeToRun = code.replace(/^--/gm, '#');
      }

      // For Python plots, the Jupyter kernel will automatically capture them
      if (language === 'python') {
        // Add a simple working example if the code is just the default
        if (code.trim() === 'import matplotlib.pyplot as plt\nimport numpy as np\n\n\nx = np.linspace(0, 10, 100)\ny = np.sin(x)\n\n\nplt.figure(figsize=(10, 6))\nplt.plot(x, y)\nplt.title(\'Sine Wave\')\nplt.xlabel(\'X\')\nplt.ylabel(\'Y\')\nplt.grid(True)\n\n\nresult = get_plot()') {
          // This is the exact example code - make sure it works by adding tight_layout
          codeToRun = 'import matplotlib.pyplot as plt\nimport numpy as np\n\n# Create data\nx = np.linspace(0, 10, 100)\ny = np.sin(x)\n\n# Create plot\nplt.figure(figsize=(10, 6))\nplt.plot(x, y)\nplt.title(\'Sine Wave\')\nplt.xlabel(\'X\')\nplt.ylabel(\'Y\')\nplt.grid(True)\nplt.tight_layout()\n\n# Display the plot\nplt.show()\n\nprint("Plot generated successfully!")\n';
        }
      }

      // Store the original code for potential input handling
      setOriginalCode(codeToRun);

      // Execute the code with proper error handling
      try {
        await onRun(id, codeToRun, shouldShowGraphicWalker);

        // Note: The result will be updated via the result prop,
        // and the input handling will be done through the QueryResult component

        // Enhanced success feedback based on result type
        if (result && result.plots && Array.isArray(result.plots) && result.plots.length > 0) {
          // Switch to output tab if we have plots
          if (onViewModeChange) {
            onViewModeChange('output');
          }
          toast.success(`Code executed successfully. ${result.plots.length} plot(s) generated!`);
        } else if (result && result.data && Array.isArray(result.data) && result.data.length > 0) {
          // DataFrame or table data generated
          if (onViewModeChange) {
            onViewModeChange('table');
          }
          toast.success(`Code executed successfully. DataFrame with ${result.data.length} rows displayed!`);
        } else if (result && result.output) {
          // Text output generated
          toast.success('Code executed successfully. Output generated!');
        } else {
          toast.success('Code executed successfully');
        }

        const endTime = new Date();
        setExecutionTime({ startTime, endTime });

        // Stop the live timer
        if (executionTimerRef.current) {
          clearInterval(executionTimerRef.current);
          executionTimerRef.current = null;
        }
      } catch (error) {
        console.error('Execution error:', error);
        const endTime = new Date();
        setExecutionTime({ startTime, endTime });

        // Stop the live timer
        if (executionTimerRef.current) {
          clearInterval(executionTimerRef.current);
          executionTimerRef.current = null;
        }

        // Show error toast
        if (error instanceof Error) {
          if (error.message.includes('Unexpected token')) {
            toast.error('Server returned an invalid response. The Python server might be down or experiencing issues.');
          } else {
            toast.error(`Execution failed: ${error.message}`);
          }
        } else {
          toast.error('Unknown execution error');
        }
      } finally {
        setIsRunning(false);
      }
    } catch (error) {
      console.error('Outer execution error:', error);
      const endTime = new Date();
      setExecutionTime({ startTime, endTime });

      // Stop the live timer
      if (executionTimerRef.current) {
        clearInterval(executionTimerRef.current);
        executionTimerRef.current = null;
      }

      setIsRunning(false);
      toast.error('Failed to execute code');
    }
  };

  // Keyboard shortcut handlers
  const keyboardHandlers: KeyboardShortcutHandlers = {
    onRun: handleRunCell,
    onDelete: () => onDelete(id),
    onAddCellAbove: () => onAddCell(id, 'code'), // Add above current cell
    onAddCellBelow: () => onAddCell(id, 'code'), // Add below current cell
    onMoveUp: onMoveUp ? () => onMoveUp(id) : undefined,
    onMoveDown: onMoveDown ? () => onMoveDown(id) : undefined,
    onConvertToMarkdown: onConvertCellType ? () => onConvertCellType(id, 'markdown') : undefined,
    onConvertToCode: onConvertCellType ? () => onConvertCellType(id, 'code') : undefined,
    onFocusEditor: () => {
      if (editorRef.current) {
        editorRef.current.focus();
      }
    },
    onToggleLanguage: () => {
      if (cellType === 'code') {
        const nextIndex = (currentLanguageIndex + 1) % languages.length;
        onLanguageChange(languages[nextIndex]);
      }
    }
  };

  // Use keyboard shortcuts hook
  useKeyboardShortcuts(keyboardHandlers, {
    cellId: id,
    isHovered,
    isRunning,
    cellType,
    language,
    editorRef,
    disabled: false
  });

  // In the Cell component, update the editor onChange handler to use debouncing
  const debouncedContentChange = useRef(
    _.debounce((value: string) => {
      if (onContentChange) {
        onContentChange(value);
      }
    }, 200)  // 200ms debounce
  ).current;

  // Add a useEffect for cleanup
  useEffect(() => {
    return () => {
      // Clean up the timeout when component unmounts
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      // Clean up the execution timer
      if (executionTimerRef.current) {
        clearInterval(executionTimerRef.current);
      }
    };
  }, []);

  // Handle notes update
  const handleNotesUpdate = (newNotes: string) => {
    if (onUpdateNotes) {
      onUpdateNotes(id, newNotes);
    }
  };

  // Handle chart type change and auto-switch to chart view
  const handleChartTypeChange = (newType: 'line' | 'bar' | 'pie' | 'area') => {
    if (onChartTypeChange) {
      onChartTypeChange(newType);
      
      // Auto-switch to chart view when chart type is changed
      if (onViewModeChange && hasChartableData) {
        onViewModeChange('chart');
      }
    }
  };

  return (
    <div
      className="relative group my-2"
      data-cell-id={id}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Add Cell Button - Now positioned at the bottom */}
      <div className={cn(
        "absolute -bottom-2 left-1/2 transform -translate-x-1/2 z-20",
        isHovered ? "opacity-100" : "opacity-0"
      )}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-5 rounded-full p-0 bg-background hover:bg-accent px-1"
            >
              <Plus className="h-3 w-3 mr-0.5" />
              <span className="text-[9px]">Add</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="center">
            <DropdownMenuItem onClick={() => onAddCell(id, 'code')}>
              <Code2 className="h-3 w-3 mr-2" />
              <span className="text-xs">Code Cell</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onAddCell(id, 'markdown')}>
              <FileText className="h-3 w-3 mr-2" />
              <span className="text-xs">Markdown Cell</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Card
        className={cn(
          "relative w-full overflow-hidden transition-all duration-300 py-0",
          cellType === 'markdown'
            ? "bg-transparent border-none shadow-none"
            : cn(
                "border",
                isHovered
                  ? (result?.error ? "border-red-500" : isSuccess ? "border-green-500" : "border-primary/50")
                  : "border-border",
                "shadow-sm"
              )
        )}
        style={{
          width: '100%',
          maxWidth: '100%',
        }}
        data-cell-id={id}
      >
        {/* Cell Number Indicator - No drag functionality */}
        <div
          className={cn(
            "absolute left-0 top-0 z-20 flex items-center justify-center h-6 w-6 rounded-br-md text-xs font-mono font-semibold",
            cellType === 'markdown' ? "text-muted-foreground/50" : ""
          )}
        >
          {typeof index === 'number' ? `[${index + 1}]` : '•'}
        </div>
        {/* Cell Actions - Conditional based on cell type */}
        <div className={cn(
          "absolute right-1 top-1 z-30 flex items-center gap-0.5",
          cellType === 'markdown'
            ? "bg-background/30 backdrop-blur-sm rounded-md px-0.5 py-0.5"
            : "bg-background/50 backdrop-blur-sm rounded-md px-0.5 py-0.5",
          "transition-opacity duration-200",
          isHovered ? "opacity-100" : "opacity-0 pointer-events-none"
        )}>

          {/* Dataset Selector - Compact version */}
          {cellType !== 'markdown' && (
            <div className="relative" data-dropdown-id={id}>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-1.5 hover:bg-accent flex items-center gap-1"
                onClick={handleDropdownToggle}
              >
                <Database className="h-3 w-3" />
                <span className="text-xs truncate max-w-[80px]">
                  {selectedDatasets.length === 1
                    ? selectedDatasets[0].name
                    : selectedDatasets.length > 1
                      ? `${selectedDatasets.length} datasets`
                      : 'Select Datasets'}
                </span>
                <ChevronDown className="h-3 w-3" />
              </Button>

              {isDropdownOpen && (
                <div
                  className="absolute top-full right-0 mt-1 w-[200px] bg-background rounded-md border shadow-lg z-[100]"
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="max-h-[200px] overflow-y-auto">
                    {availableDatasets.length === 0 ? (
                      <div className="px-2 py-1.5 text-xs text-muted-foreground">
                        {serverStatus.status === 'healthy'
                          ? 'No datasets found in database. Upload some datasets to get started.'
                          : 'No datasets available'
                        }
                      </div>
                    ) : (
                      <div className="py-1">
                        {availableDatasets.map(dataset => (
                          <div
                            key={dataset.id}
                            className="flex items-center px-2 py-1 hover:bg-accent hover:text-accent-foreground cursor-pointer"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              const isCurrentlySelected = selectedDatasets.some(d => d.id === dataset.id);
                              const currentIds = selectedDatasets.map(d => d.id);
                              const newIds = isCurrentlySelected
                                ? currentIds.filter(id => id !== dataset.id)
                                : [...currentIds, dataset.id];
                              console.log('Row clicked:', dataset.name, 'New selection:', newIds);
                              handleDatasetSelection(newIds);
                            }}
                          >
                            <Checkbox
                              id={`dataset-${dataset.id}`}
                              checked={selectedDatasets.some(d => d.id === dataset.id)}
                              onCheckedChange={(checked) => {
                                console.log('Checkbox clicked:', dataset.name, checked);
                                const currentIds = selectedDatasets.map(d => d.id);
                                const newIds = checked
                                  ? [...currentIds, dataset.id]
                                  : currentIds.filter(id => id !== dataset.id);
                                console.log('New dataset IDs:', newIds);
                                handleDatasetSelection(newIds);
                              }}
                              className="mr-2 h-3 w-3 pointer-events-auto"
                              onClick={(e) => e.stopPropagation()}
                            />
                            <label
                              htmlFor={`dataset-${dataset.id}`}
                              className="text-xs flex-1 cursor-pointer select-none"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                const isCurrentlySelected = selectedDatasets.some(d => d.id === dataset.id);
                                const currentIds = selectedDatasets.map(d => d.id);
                                const newIds = isCurrentlySelected
                                  ? currentIds.filter(id => id !== dataset.id)
                                  : [...currentIds, dataset.id];
                                handleDatasetSelection(newIds);
                              }}
                            >
                              {dataset.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Language Selector - Only for code cells */}
          {cellType !== 'markdown' && (
            <Select
              value={language}
              onValueChange={(newLang) => {
                try {
                  // When language changes, update the editor content with the default for that language
                  if (editorRef.current && newLang !== language) {
                    const currentContent = editorRef.current.getValue();
                    // Only replace content if it's empty
                    if (!currentContent.trim()) {
                      editorRef.current.setValue('');
                    }

                    // Clear any previous results when changing languages
                    // This prevents JSON parsing errors when switching languages
                    if (onViewModeChange) {
                      onViewModeChange('table'); // Reset to table view
                    }
                  }

                  // Notify parent about language change
                  onLanguageChange(newLang);

                  // Show a toast to confirm language change
                  toast.success(`Switched to ${newLang.charAt(0).toUpperCase() + newLang.slice(1)} language`);
                } catch (error) {
                  console.error('Error changing language:', error);
                  toast.error('Failed to change language');
                }
              }}
            >
              <SelectTrigger className="h-6 w-[90px] text-xs px-1.5">
                <Code2 className="h-3 w-3 mr-1" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sql" className="text-xs">SQL</SelectItem>
                <SelectItem value="python" className="text-xs">Python</SelectItem>
                <SelectItem value="javascript" className="text-xs">JavaScript</SelectItem>
              </SelectContent>
            </Select>
          )}

          {/* Run Button - Only for code cells */}
          {cellType !== 'markdown' && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRunCell}
                    disabled={isRunning}
                    className={cn(
                      "h-5 w-5 p-0",
                      "hover:bg-accent active:scale-95",
                      "transition-transform duration-100"
                    )}
                  >
                    {isRunning ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <Play className="h-3 w-3" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">
                    {isRunning
                      ? `Running... ${(currentExecutionTime / 1000).toFixed(2)}s`
                      : 'Run cell (Ctrl+Enter)'
                    }
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Move Up Button */}
          {onMoveUp && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                    onClick={() => onMoveUp(id)}
                  >
                    <MoveUp className="h-3 w-3 text-muted-foreground" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">Move cell up (Ctrl+Shift+↑)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Move Down Button */}
          {onMoveDown && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                    onClick={() => onMoveDown(id)}
                  >
                    <MoveDown className="h-3 w-3 text-muted-foreground" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">Move cell down (Ctrl+Shift+↓)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Fullscreen Editor Button for Markdown Cells */}
          {cellType === 'markdown' && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                    onClick={() => {
                      markdownCellRef.current?.openFullscreen()
                    }}
                  >
                    <Maximize2 className="h-3 w-3 text-muted-foreground" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">Fullscreen markdown editor</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Fullscreen Editor Button for Code Cells */}
          {cellType !== 'markdown' && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsFullscreenOpen(true)}
                    className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                  >
                    <Maximize2 className="h-3 w-3 text-muted-foreground" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">Fullscreen editor</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Convert Cell Type Button */}
          {onConvertCellType && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                    onClick={() =>
                      onConvertCellType(id, cellType === 'code' ? 'markdown' : 'code')
                    }
                  >
                    {cellType === 'code'
                      ? <FileText className="h-3 w-3 text-muted-foreground" />
                      : <Code2 className="h-3 w-3 text-muted-foreground" />
                    }
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">
                    Convert to {cellType === 'code' ? 'markdown' : 'code'} cell
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Edit Markdown button for markdown cells */}
          {cellType === 'markdown' && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                    onClick={() => setIsEditingMarkdown(!isEditingMarkdown)}
                  >
                    <Bold className="h-3 w-3 text-muted-foreground" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">
                    {isEditingMarkdown ? "Exit Edit Mode" : "Edit Markdown"}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* AI Assistant Button - only for code cells */}
          {cellType === 'code' && onContentChange && (
            <div className="ml-2">
              <AIAssistant
                selectedDatasets={selectedDatasets}
                language={language as 'sql' | 'python' | 'javascript' | 'markdown'}
                onCodeGenerated={(code) => {
                  onContentChange(code);
                  toast.success('AI-generated code inserted!');
                }}
                editorRef={editorRef}
              />
            </div>
          )}

          {/* Keyboard Shortcuts Help */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  <KeyboardShortcutsHelp />
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">Keyboard shortcuts</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Delete Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(id)}
            className="h-6 w-6 p-0 hover:bg-destructive hover:text-destructive-foreground"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>

        {/* Dataset Info - Even more compact */}
        {(selectedDatasets.length > 0 || cellType === 'code') && (
          <div className="px-1 pt-0 pb-0 text-[9px] text-muted-foreground flex items-center justify-between gap-1">
            <div className="flex items-center gap-1">
              <Database className="h-3 w-3" />
              <span>
               {selectedDatasets.length > 0 ? (
                 selectedDatasets.length === 1
                   ? language === 'sql'
                     ? `Using dataset: ${selectedDatasets[0].name} → SQL table: dataset1`
                     : `Using dataset: ${selectedDatasets[0].name} → pd.read_csv('dataset1.csv')`
                   : language === 'sql'
                     ? `Using ${selectedDatasets.length} datasets → SQL tables: ${selectedDatasets.map((_, idx) => `dataset${idx + 1}`).join(', ')}`
                     : `Using ${selectedDatasets.length} datasets → ${selectedDatasets.map((_, idx) => `pd.read_csv('dataset${idx + 1}.csv')`).join(', ')}`
               ) : language === 'python' ? (
                 'No UI datasets selected - use pd.read_csv() to load your own data'
               ) : language === 'sql' ? (
                 'No datasets selected - select datasets to create SQL tables'
               ) : (
                 'No datasets selected'
               )}
                <span className="text-muted-foreground ml-2">
                  {new Date().toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric'
                  })}
                </span>
              </span>
            </div>

            {/* Execution time information - more compact */}
            {(executionTime.endTime || isRunning) && (
              <span className="text-[9px] text-muted-foreground">
                {isRunning ? (
                  <span className="text-blue-600 dark:text-blue-400">
                    Running... {(currentExecutionTime / 1000).toFixed(2)}s
                  </span>
                ) : executionTime.endTime ? (
                  <>
                    {formatDistanceToNow(executionTime.endTime, { addSuffix: true })}
                    {executionTime.startTime && executionTime.endTime && (
                      <span className="ml-1">
                        ({((executionTime.endTime.getTime() - executionTime.startTime.getTime()) / 1000).toFixed(2)}s)
                      </span>
                    )}
                  </>
                ) : null}
              </span>
            )}
          </div>
        )}

        <div className="transition-colors duration-200">
          {/* Editor Area - Adjusted padding for cell number only */}
          <div className="p-0 pl-6"> {/* Add left padding to accommodate cell number */}
            {cellType === 'markdown' ? (
              <MarkdownCell
                ref={markdownCellRef}
                content={content}
                onContentChange={onContentChange}
                isEditing={isEditingMarkdown}
                setIsEditing={setIsEditingMarkdown}
              />
            ) : (
              <Editor
                height={editorHeight}
                defaultLanguage={language}
                defaultValue={content || ''}
                theme={theme === 'dark' ? 'vs-dark' : 'light'}
                options={{
                  minimap: { enabled: false },
                  lineNumbers: 'on',
                  folding: false,
                  scrollBeyondLastLine: false,
                  wordWrap: 'on',
                  contextmenu: false,
                  fontSize: 11,
                  lineHeight: 15,
                  padding: { top: 1, bottom: 1 },
                  // Enhanced syntax highlighting
                  bracketPairColorization: { enabled: true },
                  colorDecorators: true,
                  // Better suggestions
                  quickSuggestions: {
                    other: true,
                    comments: false,
                    strings: false
                  },
                  suggestOnTriggerCharacters: true,
                  acceptSuggestionOnCommitCharacter: true,
                  acceptSuggestionOnEnter: 'on',
                  tabCompletion: 'on',
                  // Code formatting
                  formatOnPaste: true,
                  formatOnType: true,
                  // Visual enhancements
                  cursorBlinking: 'smooth',
                  cursorSmoothCaretAnimation: 'on',
                  smoothScrolling: true,
                  renderLineHighlight: 'all',
                  renderWhitespace: 'selection',
                  // Language features
                  hover: { enabled: true },
                  parameterHints: { enabled: true },
                  // Selection and highlighting
                  selectionHighlight: true,
                  occurrencesHighlight: 'singleFile',
                }}
                onMount={handleEditorDidMount}
                onChange={(value) => {
                  if (typeof value === 'string') {
                    debouncedContentChange(value);
                  }
                }}
              />
            )}
          </div>

          {/* Results - More compact */}
          <div className={cn("mt-0 relative", result ? "border-t border-t-border/50" : "")} data-cell-id={id}>
            {result && cellType === 'code' && (
              <QueryResult
                data={result.data}
                output={result.output}
                plots={result.plots}
                result={result.result}
                error={result.error}
                errorDetails={result.errorDetails}
                executionTime={result.executionTime}
                isSuccess={isSuccess && !result.error}
                showGraphicWalker={showGraphicWalker}
                onSaveChart={onSaveChart}
                onSaveTable={onSaveTable}
                onSavePlot={onSavePlot}
                chartType={chartType}
                onChartTypeChange={onChartTypeChange}
                viewMode={viewMode}
                onViewModeChange={onViewModeChange}
                cellId={id}
                language={language} // Pass language to QueryResult
                // Pass cell control props
                onMoveUp={onMoveUp}
                onMoveDown={onMoveDown}
                notes={notes}
                onUpdateNotes={onUpdateNotes}
                needsInput={result?.needs_input || needsInput}
                inputPrompt={result?.input_prompt || inputPrompt}
                onInputSubmit={async (input: string) => {
                  setUserInput(input);
                  await handleInputSubmit();
                }}
              />
            )}

          </div>
        </div>
      </Card>



      {/* Fullscreen Editor */}
      <FullscreenEditor
        isOpen={isFullscreenOpen}
        onClose={() => setIsFullscreenOpen(false)}
        content={content}
        language={language}
        onContentChange={onContentChange || (() => {})}
        onRun={cellType === 'code' ? handleRunCell : undefined}
        isRunning={isRunning}
        cellId={`${index !== undefined ? index + 1 : '?'}`}
      />

      {/* Input Dialog for Python interactive input */}

    </div>
  );
}