// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model User {
  id            String     @id @default(auto()) @map("_id") @db.ObjectId
  clerkId       String     @unique
  email         String     @unique
  name          String?
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  // Created records
  notes         Note[]     @relation("NoteToUser")
  messages      Message[]  @relation("MessageToUser")
  reactions     Reaction[] @relation("ReactionToUser")
  diagrams      Diagram[]

  // Channel relations
  createdChannels Channel[] @relation("ChannelCreator")
  memberChannels  Channel[] @relation("ChannelMembers", fields: [memberChannelIds], references: [id])
  memberChannelIds String[] @db.ObjectId
  userRoles       UserRole[]
  pdfDocuments    PDFDocument[]
  datasets        DataSet[]
  datasetFolders  DataSetFolder[] @relation("UserToFolders")
  workspaces      Workspace[]
}



model Note {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  content     Json      // Changed from String to Json
  coverImage  String?
  isFolder    Boolean   @default(false)
  isPublished Boolean   @default(false)
  publishedAt DateTime?
  publicId    String?   // For public sharing URL
  metadata    String?   // Added for storing merge information
  user        User      @relation("NoteToUser", fields: [userId], references: [id])
  userId      String    @db.ObjectId
  parent      Note?     @relation("NoteToNote", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  parentId    String?   @db.ObjectId
  children    Note[]    @relation("NoteToNote")
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([userId])
  @@index([parentId])
  @@index([publicId])
}

model Channel {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?

  // Creator relation
  creator     User      @relation("ChannelCreator", fields: [creatorId], references: [id])
  creatorId   String    @db.ObjectId

  // Members relation
  members     User[]    @relation("ChannelMembers", fields: [memberIds], references: [id])
  memberIds   String[]  @db.ObjectId @default([])

  messages    Message[]
  userRoles   UserRole[]
  inviteToken String?   @unique

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @default(now())

  @@index([creatorId])
  @@index([memberIds])
}

model UserRole {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  user      User     @relation(fields: [userId], references: [id])
  userId    String   @db.ObjectId
  channel   Channel  @relation(fields: [channelId], references: [id])
  channelId String   @db.ObjectId
  role      String   // "OWNER", "ADMIN", "MEMBER"

  @@index([userId])
  @@index([channelId])
  @@unique([userId, channelId])
}

model Message {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  content   String
  fileUrl   String?  // Add this field
  fileKey   String?
  fileType  String?
  userId    String   @db.ObjectId
  channelId String   @db.ObjectId
  parentId  String?  @db.ObjectId
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user      User     @relation("MessageToUser", fields: [userId], references: [id])
  channel   Channel  @relation(fields: [channelId], references: [id])
  parent    Message? @relation("ReplyTo", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  replies   Message[] @relation("ReplyTo")
  reactions Reaction[]

  @@index([userId])
  @@index([channelId])
  @@index([parentId])
}

model Reaction {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  user      User     @relation("ReactionToUser", fields: [userId], references: [id])
  userId    String   @db.ObjectId
  emoji     String
  message   Message  @relation(fields: [messageId], references: [id])
  messageId String   @db.ObjectId
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@index([userId])
  @@index([messageId])
}


model Diagram {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  content     Json
  clerkId     String
  user        User     @relation(fields: [clerkId], references: [clerkId])
  isPublic    Boolean  @default(false)
  publicId    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())

  @@unique([publicId])
  @@index([clerkId])
}


model PDFDocument {
  id             String    @id @default(auto()) @map("_id") @db.ObjectId
  fileName       String
  fileSize       Int
  userId         String    @db.ObjectId
  user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt      DateTime  @default(now())
  vectorId       String?   // ID in the vector database for future RAG system
  embedding      Boolean   @default(false) // Whether this document has been embedded
  embeddingModel String?   // The model used for embedding (for future RAG system)

  @@index([userId])
  @@unique([fileName, userId]) // Ensure no duplicate filenames per user
}

model DataSetFolder {
  id             String          @id @default(auto()) @map("_id") @db.ObjectId
  name           String
  description    String?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  userId         String         @db.ObjectId
  user           User           @relation("UserToFolders", fields: [userId], references: [id])

  // Folder hierarchy
  parent         DataSetFolder? @relation("FolderToFolder", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  parentId       String?        @db.ObjectId
  children       DataSetFolder[] @relation("FolderToFolder")

  // Datasets in this folder
  datasets       DataSet[]      @relation("FolderToDatasets")

  @@index([userId])
  @@index([parentId])
}

model DataSet {
  id             String          @id @default(auto()) @map("_id") @db.ObjectId
  name           String
  description    String?
  data           Json           // Store the actual data
  headers        String[]       // Store column headers
  fileType       String         // "csv" or "excel"
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  userId         String         @db.ObjectId
  user           User           @relation(fields: [userId], references: [id])
  changes        DataSetChange[] // Add this relation
  vectorId       String?        // ID in the vector database
  embedding      Boolean        @default(false) // Whether this dataset has been embedded
  embeddingModel String?        // The model used for embedding (gemini, cohere, mistral, etc.)

  // Folder relation
  folder         DataSetFolder? @relation("FolderToDatasets", fields: [folderId], references: [id])
  folderId       String?        @db.ObjectId

  @@index([userId])
  @@index([vectorId])
  @@index([folderId])
}

// New model for tracking changes
model DataSetChange {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  dataSet       DataSet   @relation(fields: [dataSetId], references: [id], onDelete: Cascade)
  dataSetId     String    @db.ObjectId
  userId        String    @db.ObjectId
  userName      String    // Store the user's name who made the change
  changes       Json      // Store the changes made
  timestamp     DateTime  @default(now())
  version       Int       // Version number

  @@index([dataSetId])
  @@index([userId])
}

// Workspace models for notebook and dashboard functionality
model Workspace {
  id             String          @id @default(auto()) @map("_id") @db.ObjectId
  name           String
  description    String?
  isPublic       Boolean         @default(false)
  publicId       String?         @unique // For public sharing URL
  userId         String          @db.ObjectId
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Workspace settings
  settings       Json?           // Store workspace-level settings (theme, layout preferences, etc.)

  // Relations
  notebooks      Notebook[]
  dashboards     Dashboard[]

  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt

  @@index([userId])
}

model Notebook {
  id             String          @id @default(auto()) @map("_id") @db.ObjectId
  name           String
  description    String?
  workspaceId    String          @db.ObjectId
  workspace      Workspace       @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  // Notebook content and state
  cells          NotebookCell[]
  cellOrder      String[]        // Array of cell IDs to maintain order

  // Notebook settings
  settings       Json?           // Store notebook-level settings

  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt

  @@index([workspaceId])
}

model NotebookCell {
  id             String          @id @default(auto()) @map("_id") @db.ObjectId
  notebookId     String          @db.ObjectId
  notebook       Notebook        @relation(fields: [notebookId], references: [id], onDelete: Cascade)

  // Cell content and metadata
  cellType       String          // "code" | "markdown"
  language       String?         // "sql" | "python" | "javascript" | "markdown"
  content        String          // The actual code/markdown content

  // Execution results
  result         Json?           // Store execution results (data, output, plots, etc.)
  error          String?         // Store error messages
  errorDetails   Json?           // Store detailed error information
  executionTime  Int?            // Execution time in milliseconds
  isSuccess      Boolean         @default(false)

  // Cell metadata
  selectedDatasetIds String[]     @db.ObjectId // IDs of selected datasets
  notes          String?         // Cell notes
  order          Int             // Order within the notebook

  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt

  @@index([notebookId])
  @@index([order])
}

model Dashboard {
  id             String          @id @default(auto()) @map("_id") @db.ObjectId
  name           String
  description    String?
  workspaceId    String          @db.ObjectId
  workspace      Workspace       @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  // Dashboard content
  items          DashboardItem[]
  layout         Json?           // Store dashboard layout configuration

  // Dashboard settings
  settings       Json?           // Store dashboard-level settings (theme, auto-refresh, etc.)

  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt

  @@index([workspaceId])
}

model DashboardItem {
  id             String          @id @default(auto()) @map("_id") @db.ObjectId
  dashboardId    String          @db.ObjectId
  dashboard      Dashboard       @relation(fields: [dashboardId], references: [id], onDelete: Cascade)

  // Item type and content
  type           String          // "chart" | "table" | "text" | "heading" | "pythonplot" | "calculator"
  title          String?
  description    String?

  // Item data and configuration
  data           Json?           // Store the actual data for charts/tables
  config         Json?           // Store configuration (chart type, styling, etc.)
  content        String?         // For text/heading items

  // Layout properties
  gridColumn     Int             @default(1)
  gridRow        Int             @default(1)
  width          Int             @default(1)
  height         Int             @default(1)

  // Source information
  sourceNotebookId String?       @db.ObjectId // Which notebook this item came from
  sourceCellId   String?         @db.ObjectId // Which cell this item came from

  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt

  @@index([dashboardId])
  @@index([sourceNotebookId])
  @@index([sourceCellId])
}
